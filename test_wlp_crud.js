#!/usr/bin/env node

/**
 * Comprehensive CRUD test for W<PERSON> Templates with real JWT token
 */

const https = require('https');
const fs = require('fs');

// Test configuration
const API_BASE_URL = 'https://localhost:7001';
const APPLICATION_ID = '03FC0B90-DFAD-11EE-8D86-0800200C9A66';

// Real JWT token provided by user
const REAL_JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLjbSgUD8t4OsO986AbYvl_OVJhsEaSkMOGUYzB3gic';

// Function to generate UUID v4
function uuidv4() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// Function to create request envelope
function createRequestEnvelope(payload, userId = '08ddcb9c-29ab-49d8-a7b9-faccf5caf282') {
  return {
    Header: {
      Id: uuidv4(),
      Application: APPLICATION_ID,
      Bank: "BANK",
      UserId: userId
    },
    Payload: payload
  };
}

// Function to make HTTPS request
function makeRequest(endpoint, data, token) {
  return new Promise((resolve, reject) => {
    const url = new URL(`${API_BASE_URL}${endpoint}`);

    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json'
      },
      rejectUnauthorized: false
    };

    const req = https.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsedData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(JSON.stringify(data));
    req.end();
  });
}

// Test functions
async function listTemplates() {
  console.log('\n=== 1. LIST TEMPLATES ===');

  const payload = {
    searchTerm: "",
    pageNumber: 1,
    pageSize: 50
  };

  const requestData = createRequestEnvelope(payload);
  const response = await makeRequest('/api/WlpTemplate/list', requestData, REAL_JWT_TOKEN);

  console.log('Status:', response.statusCode);

  if (response.statusCode === 200) {
    const templates = response.data.payload || [];
    console.log(`✅ Found ${templates.length} template(s):`);

    templates.forEach((template, index) => {
      console.log(`  ${index + 1}. ${template.name} (ID: ${template.id})`);
      console.log(`     File: ${template.fileName}, Zones: ${template.zoneCount}`);
    });

    return templates;
  } else {
    console.log('❌ Failed to list templates');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return [];
  }
}

async function createTemplate() {
  console.log('\n=== 2. CREATE TEMPLATE ===');

  // Use the real WLP file that we know works
  const wlpFilePath = './SmartBoat.API/Implementations/GeoFencingService/geofenceData.wlp';

  if (!fs.existsSync(wlpFilePath)) {
    console.log('❌ WLP file not found:', wlpFilePath);
    return null;
  }

  const wlpFileContent = fs.readFileSync(wlpFilePath, 'utf8');
  const base64Content = Buffer.from(wlpFileContent).toString('base64');

  const payload = {
    name: `CRUD Test Template ${Date.now()}`,
    description: "Template created for CRUD testing",
    fileName: "crud_test.wlp",
    originalFilename: "crud_test.wlp",
    fileContentBase64: base64Content
  };

  const requestData = createRequestEnvelope(payload);
  const response = await makeRequest('/api/WlpTemplate/create', requestData, REAL_JWT_TOKEN);

  console.log('Status:', response.statusCode);

  if (response.statusCode === 200) {
    console.log('✅ Template created successfully');
    console.log('Created template:', JSON.stringify(response.data.payload, null, 2));
    return response.data.payload;
  } else {
    console.log('❌ Failed to create template');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return null;
  }
}

async function getTemplate(templateId) {
  console.log('\n=== 3. GET TEMPLATE ===');
  console.log('Template ID:', templateId);

  const payload = {
    templateId: templateId
  };

  const requestData = createRequestEnvelope(payload);
  const response = await makeRequest('/api/WlpTemplate/get', requestData, REAL_JWT_TOKEN);

  console.log('Status:', response.statusCode);

  if (response.statusCode === 200) {
    console.log('✅ Template retrieved successfully');
    console.log('Template details:', JSON.stringify(response.data.payload, null, 2));
    return response.data.payload;
  } else {
    console.log('❌ Failed to get template');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return null;
  }
}

async function updateTemplate(templateId) {
  console.log('\n=== 4. UPDATE TEMPLATE ===');
  console.log('Template ID:', templateId);

  const payload = {
    id: templateId,
    name: `Updated CRUD Test Template ${Date.now()}`,
    description: "Updated description for CRUD testing"
  };

  const requestData = createRequestEnvelope(payload);
  const response = await makeRequest('/api/WlpTemplate/update', requestData, REAL_JWT_TOKEN);

  console.log('Status:', response.statusCode);

  if (response.statusCode === 200) {
    console.log('✅ Template updated successfully');
    console.log('Updated template:', JSON.stringify(response.data.payload, null, 2));
    return response.data.payload;
  } else {
    console.log('❌ Failed to update template');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return null;
  }
}

async function deleteTemplate(templateId) {
  console.log('\n=== 5. DELETE TEMPLATE ===');
  console.log('Template ID:', templateId);

  const payload = {
    templateId: templateId
  };

  const requestData = createRequestEnvelope(payload);
  const response = await makeRequest('/api/WlpTemplate/delete', requestData, REAL_JWT_TOKEN);

  console.log('Status:', response.statusCode);

  if (response.statusCode === 200) {
    console.log('✅ Template deleted successfully');
    console.log('Delete response:', JSON.stringify(response.data, null, 2));
    return true;
  } else {
    console.log('❌ Failed to delete template');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return false;
  }
}

// Main test function
async function testWlpCrud() {
  console.log('🧪 Testing WLP Template CRUD Operations with Real JWT Token');
  console.log('='.repeat(60));

  try {
    // 1. List existing templates
    const initialTemplates = await listTemplates();

    // 2. Create a new template
    const createdTemplate = await createTemplate();
    if (!createdTemplate) {
      console.log('❌ Cannot continue CRUD test - template creation failed');
      return;
    }

    const templateId = createdTemplate.id;
    console.log(`\n📝 Using template ID: ${templateId} for remaining tests`);

    // 3. Get the created template
    await getTemplate(templateId);

    // 4. Update the template
    await updateTemplate(templateId);

    // 5. List templates again to verify update
    console.log('\n=== VERIFY UPDATE ===');
    await listTemplates();

    // 6. Delete the template
    const deleteSuccess = await deleteTemplate(templateId);

    // 7. List templates again to verify deletion
    console.log('\n=== VERIFY DELETION ===');
    const finalTemplates = await listTemplates();

    // 8. Try to get the deleted template (should fail)
    console.log('\n=== VERIFY TEMPLATE IS GONE ===');
    await getTemplate(templateId);

    console.log('\n' + '='.repeat(60));
    console.log('🏁 CRUD Test Summary:');
    console.log(`   Initial templates: ${initialTemplates.length}`);
    console.log(`   Final templates: ${finalTemplates.length}`);
    console.log(`   Delete operation: ${deleteSuccess ? '✅ Success' : '❌ Failed'}`);

    if (deleteSuccess && finalTemplates.length === initialTemplates.length) {
      console.log('✅ CRUD operations working correctly - template properly deleted');
    } else {
      console.log('❌ ISSUE DETECTED - template may still exist in database');
    }

  } catch (error) {
    console.error('❌ CRUD Test Error:', error.message);
  }
}

// Run the test
testWlpCrud();
