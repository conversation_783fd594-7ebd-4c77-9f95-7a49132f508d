/**
 * Company Query Hooks
 * Custom React Query hooks for company data
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { companyService } from '../../services';
import { queryKeys } from '../../config/queryClient';

/**
 * Hook to fetch all companies with pagination support
 */
export const useCompanies = (options = {}) => {
  // Extract service options from React Query options
  const {
    pageLimit,
    pageOffset,
    sortField,
    sortOrder,
    searchTerm,
    name,
    status,
    customerId,
    useMockFallback,
    ...queryOptions
  } = options;

  // Create query key that includes pagination and filter parameters
  const queryKey = [
    ...queryKeys.companies.lists(),
    {
      pageLimit,
      pageOffset,
      sortField,
      sortOrder,
      searchTerm,
      name,
      status,
      customerId
    }
  ];

  return useQuery({
    queryKey,
    queryFn: () => companyService.getAllCompanies({
      pageLimit,
      pageOffset,
      sortField,
      sortOrder,
      searchTerm,
      name,
      status,
      customerId,
      useMockFallback
    }),
    ...queryOptions,
  });
};

/**
 * Hook to fetch a company by ID
 */
export const useCompany = (id, options = {}) => {
  // Extract service options from React Query options
  const { useMockFallback, ...queryOptions } = options;

  return useQuery({
    queryKey: queryKeys.companies.detail(id),
    queryFn: () => companyService.getCompanyById(id, { useMockFallback }),
    enabled: !!id,
    ...queryOptions,
  });
};

/**
 * Hook to fetch vessels for a company
 */
export const useCompanyVessels = (id, options = {}) => {
  // Extract service options from React Query options
  const { useMockFallback, ...queryOptions } = options;

  return useQuery({
    queryKey: queryKeys.companies.vessels(id),
    queryFn: () => companyService.getCompanyVessels(id, { useMockFallback }),
    enabled: !!id,
    ...queryOptions,
  });
};

/**
 * Hook to fetch customers for a company
 */
export const useCompanyCustomers = (id, options = {}) => {
  return useQuery({
    queryKey: queryKeys.companies.customers(id),
    queryFn: () => companyService.getCompanyCustomers(id),
    enabled: !!id,
    ...options,
  });
};

/**
 * Hook to fetch subscriptions for a company
 */
export const useCompanySubscriptions = (id, options = {}) => {
  return useQuery({
    queryKey: queryKeys.companies.subscriptions(id),
    queryFn: () => companyService.getCompanySubscriptions(id),
    enabled: !!id,
    ...options,
  });
};

/**
 * Hook to create a new company
 */
export const useCreateCompany = (options = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (companyData) => companyService.createCompany(companyData),
    onSuccess: () => {
      // Invalidate companies list
      queryClient.invalidateQueries(queryKeys.companies.lists());
    },
    ...options,
  });
};

/**
 * Hook to update a company
 */
export const useUpdateCompany = (options = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, companyData }) => companyService.updateCompany(id, companyData),
    onSuccess: (_, variables) => {
      // Invalidate specific company and list
      queryClient.invalidateQueries(queryKeys.companies.detail(variables.id));
      queryClient.invalidateQueries(queryKeys.companies.lists());
    },
    ...options,
  });
};

/**
 * Hook to delete a company
 */
export const useDeleteCompany = (options = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id) => companyService.deleteCompany(id),
    onSuccess: (_, id) => {
      // Invalidate company lists with immediate refetch
      queryClient.invalidateQueries({
        queryKey: queryKeys.companies.lists(),
        refetchType: 'all'
      });

      // Remove company from cache
      queryClient.removeQueries(queryKeys.companies.detail(id));
      queryClient.removeQueries(queryKeys.companies.vessels(id));
      queryClient.removeQueries(queryKeys.companies.customers(id));
      queryClient.removeQueries(queryKeys.companies.subscriptions(id));
    },
    ...options,
  });
};