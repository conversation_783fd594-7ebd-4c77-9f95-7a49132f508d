/**
 * Owner Query Hooks
 * Custom React Query hooks for owner data
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ownerService } from '../../services';
import { queryKeys } from '../../config/queryClient';

/**
 * Hook to fetch all owners with pagination support
 */
export const useOwners = (options = {}) => {
  // Extract service options from React Query options
  const {
    pageLimit,
    pageOffset,
    sortField,
    sortOrder,
    searchTerm,
    name,
    status,
    companyId,
    useMockFallback,
    ...queryOptions
  } = options;

  // Create query key that includes pagination and filter parameters
  const queryKey = [
    ...queryKeys.owners.lists(),
    {
      pageLimit,
      pageOffset,
      sortField,
      sortOrder,
      searchTerm,
      name,
      status,
      companyId
    }
  ];

  return useQuery({
    queryKey,
    queryFn: () => ownerService.getAllOwners({
      pageLimit,
      pageOffset,
      sortField,
      sortOrder,
      searchTerm,
      name,
      status,
      companyId,
      useMockFallback
    }),
    ...queryOptions,
  });
};

/**
 * Hook to fetch an owner by ID
 */
export const useOwner = (id, options = {}) => {
  // Extract service options from React Query options
  const { useMockFallback, ...queryOptions } = options;

  return useQuery({
    queryKey: queryKeys.owners.detail(id),
    queryFn: () => ownerService.getOwnerById(id, { useMockFallback }),
    enabled: !!id,
    ...queryOptions,
  });
};

/**
 * Hook to fetch owners by company
 */
export const useOwnersByCompany = (companyId, options = {}) => {
  // Extract service options from React Query options
  const { useMockFallback = false, ...queryOptions } = options;

  console.log('🔄 useOwnersByCompany: Called with companyId:', companyId);
  console.log('🔄 useOwnersByCompany: Options:', options);

  return useQuery({
    queryKey: queryKeys.owners.byCompany(companyId),
    queryFn: async () => {
      console.log('🔄 useOwnersByCompany: Query function executing for companyId:', companyId);
      try {
        const result = await ownerService.getOwnersByCompany(companyId, { useMockFallback });
        console.log('✅ useOwnersByCompany: Query successful, result:', result);
        return result;
      } catch (error) {
        console.error('❌ useOwnersByCompany: Query failed:', error);
        throw error;
      }
    },
    enabled: !!companyId,
    onSuccess: (data) => {
      console.log('✅ useOwnersByCompany: onSuccess called with data:', data);
    },
    onError: (error) => {
      console.error('❌ useOwnersByCompany: onError called with error:', error);
    },
    ...queryOptions,
  });
};

/**
 * Hook to create a new owner
 */
export const useCreateOwner = (options = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ownerData) => ownerService.createOwner(ownerData),
    onSuccess: (_, variables) => {
      // Invalidate owners list
      queryClient.invalidateQueries(queryKeys.owners.lists());
      // Invalidate company-specific owners list if companyId is provided
      if (variables.companyId) {
        queryClient.invalidateQueries(queryKeys.owners.byCompany(variables.companyId));
      }
    },
    ...options,
  });
};

/**
 * Hook to update an owner
 */
export const useUpdateOwner = (options = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, ownerData, originalCompanyId }) => ownerService.updateOwner(id, ownerData),
    onSuccess: (_, variables) => {
      // Invalidate specific owner and list
      queryClient.invalidateQueries(queryKeys.owners.detail(variables.id));
      queryClient.invalidateQueries(queryKeys.owners.lists());

      // Invalidate company-specific owners lists
      if (variables.ownerData.companyId) {
        queryClient.invalidateQueries(queryKeys.owners.byCompany(variables.ownerData.companyId));
      }

      // If company changed, also invalidate the old company's owner list
      if (variables.originalCompanyId && variables.originalCompanyId !== variables.ownerData.companyId) {
        queryClient.invalidateQueries(queryKeys.owners.byCompany(variables.originalCompanyId));
      }
    },
    ...options,
  });
};

/**
 * Hook to delete an owner
 */
export const useDeleteOwner = (options = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id) => ownerService.deleteOwner(id),
    onSuccess: (_, id) => {
      // Invalidate owner lists with immediate refetch
      queryClient.invalidateQueries({
        queryKey: queryKeys.owners.lists(),
        refetchType: 'all'
      });

      // Remove owner from cache
      queryClient.removeQueries(queryKeys.owners.detail(id));

      // Invalidate all company-specific owner lists (since we don't know which company the deleted owner belonged to)
      queryClient.invalidateQueries({
        predicate: (query) => query.queryKey[0] === 'owners' && query.queryKey[2] === 'byCompany'
      });
    },
    ...options,
  });
};