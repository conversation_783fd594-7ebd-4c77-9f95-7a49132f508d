import { useQuery } from '@tanstack/react-query';
import geoFencingService from '../../services/geoFencingService';

/**
 * Query keys for geo-fencing related data
 */
export const geoFencingKeys = {
  all: ['geoFencing'],
  vesselEvents: (vesselId) => [...geoFencingKeys.all, 'vesselEvents', vesselId],
  vesselEventsWithFilters: (vesselId, filters) => [...geoFencingKeys.vesselEvents(vesselId), filters],
  activeEvents: () => [...geoFencingKeys.all, 'activeEvents'],
  coordinates: (lat, lng) => [...geoFencingKeys.all, 'coordinates', lat, lng],
};

/**
 * Hook to fetch fence events for a specific vessel
 * @param {string} vesselId - Vessel ID
 * @param {Object} filters - Filters for the events
 * @param {string} [filters.status] - Filter by status
 * @param {Date} [filters.fromDate] - Filter from date
 * @param {Date} [filters.toDate] - Filter to date
 * @param {number} [filters.pageLimit] - Results per page
 * @param {number} [filters.pageOffset] - Pagination offset
 * @param {Object} options - React Query options
 */
export const useVesselFenceEvents = (vesselId, filters = {}, options = {}) => {
  return useQuery({
    queryKey: geoFencingKeys.vesselEventsWithFilters(vesselId, filters),
    queryFn: () => geoFencingService.getVesselFenceEvents({ vesselId, ...filters }),
    enabled: !!vesselId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    ...options,
  });
};

/**
 * Hook to fetch all active fence events (vessels currently outside fence)
 * @param {Object} options - React Query options
 */
export const useActiveFenceEvents = (options = {}) => {
  return useQuery({
    queryKey: geoFencingKeys.activeEvents(),
    queryFn: () => geoFencingService.getActiveFenceEvents(),
    staleTime: 2 * 60 * 1000, // 2 minutes - more frequent updates for active events
    refetchInterval: 5 * 60 * 1000, // Auto-refresh every 5 minutes
    retry: 2,
    ...options,
  });
};

/**
 * Hook to check if coordinates are inside the geo-fence
 * @param {number} lat - Latitude
 * @param {number} lng - Longitude
 * @param {Object} options - React Query options
 */
export const useCheckCoordinates = (lat, lng, options = {}) => {
  return useQuery({
    queryKey: geoFencingKeys.coordinates(lat, lng),
    queryFn: () => geoFencingService.checkCoordinates(lat, lng),
    enabled: lat != null && lng != null,
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 1,
    ...options,
  });
};