/**
 * Formats the duration since a timestamp in a human-readable format
 * @param timestamp - ISO timestamp string
 * @returns Formatted duration string (e.g., "2h 30m", "45m", "1d 3h")
 */
export const formatDuration = (timestamp: string): string => {
  const now = new Date();
  const past = new Date(timestamp);
  const diffMs = now.getTime() - past.getTime();
  
  const minutes = Math.floor(diffMs / (1000 * 60));
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (days > 0) {
    const remainingHours = hours % 24;
    return remainingHours > 0 ? `${days}d ${remainingHours}h` : `${days}d`;
  } else if (hours > 0) {
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  } else {
    return `${minutes}m`;
  }
};

/**
 * Formats coordinates in a readable format
 * @param lat - Latitude
 * @param lng - Longitude
 * @returns Formatted coordinate string (e.g., "37.9755, 23.7348")
 */
export const formatCoordinates = (lat: number, lng: number): string => {
  return `${lat.toFixed(4)}, ${lng.toFixed(4)}`;
};

/**
 * Formats a status for display
 * @param status - Status string
 * @returns Formatted status for UI display
 */
export const formatStatus = (status: string): string => {
  switch (status?.toLowerCase()) {
    case 'active':
      return 'Outside Fence';
    case 'completed':
      return 'Returned to Fence';
    default:
      return status || 'Unknown';
  }
};

/**
 * Gets the appropriate color class for a fence event status
 * @param status - Status string
 * @returns CSS class for status styling
 */
export const getStatusColor = (status: string): string => {
  switch (status?.toLowerCase()) {
    case 'active':
      return 'text-red-600 dark:text-red-400';
    case 'completed':
      return 'text-green-600 dark:text-green-400';
    default:
      return 'text-gray-600 dark:text-gray-400';
  }
};