/**
 * API Client for the SmartBoat Platform
 * Handles API requests with consistent error handling and authentication
 */

import { API_BASE_URL, API_VERSION, REQUEST_TIMEOUT, FEATURES, APPLICATION_ID, IS_PRODUCTION, getApiBaseUrl } from '../config/envConfig';
import { getAuthToken, clearAuthToken } from '../utils/authUtils';
import { getRandomDelay } from './mockConfig';
import { mockVesselData, mockSensorData, mockCompanies, mockCustomers, mockSubscriptions, mockUsers, mockDashboard } from '../utils/mockDataUtils';
import { mockVesselFenceEvents, mockActiveFenceEvents } from '../utils/mockData';
import { v4 as uuidv4 } from 'uuid';

// Default request options
const defaultHeaders = {
  'Content-Type': 'application/json',
};

/**
 * Adds artificial delay to mock data for better UX testing
 */
const addMockDelay = (mockDataPromise) => {
  return new Promise((resolve, reject) => {
    const delay = getRandomDelay();
    setTimeout(async () => {
      try {
        const result = await mockDataPromise;
        if (result === null) {
          reject(new Error('Mock data not available'));
        } else {
          resolve(result);
        }
      } catch (error) {
        reject(error);
      }
    }, delay);
  });
};

/**
 * Creates request envelope for SmartBoat API
 */
const createRequestEnvelope = (payload = {}) => {
  // Try to get the current user from storage to include UserId in header
  let currentUser = null;
  try {
    const savedUser = localStorage.getItem('smartboat_user') || sessionStorage.getItem('smartboat_user');
    if (savedUser) {
      currentUser = JSON.parse(savedUser);
    }
  } catch (error) {
    console.warn('Failed to parse user for request envelope:', error);
  }

  return {
    Header: {
      Id: uuidv4(),
      Application: APPLICATION_ID,
      Bank: "BANK",
      UserId: currentUser?.id || null // Add UserId from current user
    },
    Payload: payload
  };
};

/**
 * Handles SmartBoat API response envelope (or raw JSON)
 * - If response follows the standard envelope, return its Payload
 * - Otherwise return the raw data (supports arrays, primitives, or plain objects)
 */
const handleEnvelopeResponse = (data) => {
  if (data && typeof data === 'object' && 'exception' in data && data.exception) {
    const error = new Error((data.exception && data.exception.description) || 'API Error');
    error.code = data.exception && data.exception.code;
    error.id = data.exception && data.exception.id;
    throw error;
  }

  // If the response is an envelope, unwrap it; else return as-is
  if (data && typeof data === 'object' && ('Payload' in data || 'payload' in data)) {
    return data.Payload ?? data.payload;
  }
  return data;
};

/**
 * Creates request headers with authentication if token exists
 */
const createHeaders = (customHeaders = {}) => {
  const token = getAuthToken();
  const headers = {
    ...defaultHeaders,
    ...customHeaders,
  };

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  return headers;
};

/**
 * Handles API response and error parsing
 */
const handleResponse = async (response, endpoint) => {
  // Handle unauthorized responses
  if (response.status === 401) {
    clearAuthToken();
    
    // Clear all auth-related storage
    localStorage.removeItem('smartboat_user');
    localStorage.removeItem('smartboat_token');
    sessionStorage.removeItem('smartboat_user');
    sessionStorage.removeItem('smartboat_token');
    
    // Trigger logout event for components listening
    window.dispatchEvent(new CustomEvent('auth:logout'));
    
    // Redirect to login
    window.location.href = '/login';
    throw new Error('Authentication expired');
  }

  // Handle other error responses
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    const error = new Error(errorData.message || response.statusText);
    error.status = response.status;
    error.data = errorData;
    throw error;
  }

  // Check if response is empty
  const contentType = response.headers.get('content-type');
  if (contentType && contentType.includes('application/json')) {
    const data = await response.json();

    // Auth endpoints don't use envelope format
    if (endpoint && endpoint.includes('/api/auth/')) {
      return data;
    }

    // Other endpoints use envelope format
    return handleEnvelopeResponse(data);
  }

  return response.text();
};

/**
 * Creates a full API url from the endpoint path
 */
const createUrl = (endpoint) => {
  const baseUrl = getApiBaseUrl();
  
  // If we have an empty base URL (using same origin), just return the endpoint
  if (baseUrl === '' || (IS_PRODUCTION && API_BASE_URL === '')) {
    return endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  }
  
  // For absolute base URLs, concatenate normally
  const fullBaseUrl = `${baseUrl}${API_VERSION ? `/${API_VERSION}` : ''}`;
  return `${fullBaseUrl}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
};

/**
 * Handles API errors with optional fallback to mock data
 */
const handleApiError = (error, endpoint, useMockFallback = false) => {
  console.error(`API Error [${endpoint}]:`, error);

  // Always throw error instead of returning mock data
  // This ensures proper error handling in the UI
  throw error;
  
  // DEPRECATED: Mock fallback logic removed to ensure proper error states
  /*
  // If mock fallback is enabled, try to return mock data based on endpoint
  if (useMockFallback && FEATURES.USE_MOCK_FALLBACK) {
    console.warn(`Falling back to mock data for ${endpoint}`);

    // Check for specific endpoints to return the appropriate mock data
    if (endpoint.includes('/vessels')) {
      if (endpoint.match(/\/vessels\/[^/]+$/)) {
        const id = endpoint.split('/').pop();
        return mockVesselData.getById(id);
      }
      if (endpoint.includes('/list')) {
        return mockVesselData.getAll();
      }
      return mockVesselData.getAll();
    }

    if (endpoint.includes('/sensor')) {
      if (endpoint.match(/\/sensor\/[^/]+$/)) {
        const id = endpoint.split('/').pop();
        return mockSensorData.getById(id);
      }
      if (endpoint.includes('/list')) {
        return mockSensorData.getAll();
      }
      return mockSensorData.getAll();
    }

    if (endpoint.includes('/companies')) {
      // Handle company vessels endpoint: /api/company/{id}/vessels
      if (endpoint.match(/\/company\/[^/]+\/vessels$/)) {
        const pathParts = endpoint.split('/');
        const companyId = pathParts[pathParts.length - 2]; // Get ID before /vessels
        return addMockDelay(mockVesselData.getByCompanyId(companyId));
      }
      if (endpoint.match(/\/companies\/[^/]+$/)) {
        const id = endpoint.split('/').pop();
        return mockCompanies.getById(id);
      }
      if (endpoint.includes('/list')) {
        return mockCompanies.getAll();
      }
      return mockCompanies.getAll();
    }

    if (endpoint.includes('/customers')) {
      if (endpoint.match(/\/customers\/[^/]+$/)) {
        const id = endpoint.split('/').pop();
        return mockCustomers.getById(id);
      }
      if (endpoint.includes('/list')) {
        return mockCustomers.getAll();
      }
      return mockCustomers.getAll();
    }

    if (endpoint.includes('/subscriptions')) {
      return mockSubscriptions.getAll();
    }

    if (endpoint.includes('/users')) {
      return mockUsers.getCurrentUser();
    }

    if (endpoint.includes('/dashboard')) {
      if (endpoint.includes('/stats')) return mockDashboard.getStats();
      if (endpoint.includes('/activity')) return mockDashboard.getRecentActivity();
      if (endpoint.includes('/vessels')) return mockDashboard.getVesselSummaries();
      if (endpoint.includes('/sensors')) return mockDashboard.getSensorSummaries();
    }
  }

  throw error;
  */
};

/**
 * API client with methods for common HTTP verbs
 */
const apiClient = {
  /**
   * Sends a GET request to the API
   */
  get: async (endpoint, options = {}) => {
    // If mock fallback is enabled, try to use mock data directly
    if (FEATURES.USE_MOCK_FALLBACK && options.useMockFallback !== false) {
      // Check for specific endpoints to return the appropriate mock data
      if (endpoint.includes('/vessels')) {
        if (endpoint.match(/\/vessels\/[^/]+$/)) {
          const id = endpoint.split('/').pop();
          return addMockDelay(mockVesselData.getById(id));
        }
        return addMockDelay(mockVesselData.getAll());
      }

      if (endpoint.includes('/sensor')) {
        if (endpoint.match(/\/sensor\/[^/]+$/)) {
          const id = endpoint.split('/').pop();
          return addMockDelay(mockSensorData.getById(id));
        }
        return addMockDelay(mockSensorData.getAll());
    }

      if (endpoint.includes('/companies')) {
        if (endpoint.match(/\/companies\/[^/]+$/)) {
          const id = endpoint.split('/').pop();
          return addMockDelay(mockCompanies.getById(id));
        }
        return addMockDelay(mockCompanies.getAll());
      }

      if (endpoint.includes('/customers')) {
        if (endpoint.match(/\/customers\/[^/]+$/)) {
          const id = endpoint.split('/').pop();
          return addMockDelay(mockCustomers.getById(id));
        }
        return addMockDelay(mockCustomers.getAll());
      }

      if (endpoint.includes('/subscriptions')) {
        return addMockDelay(mockSubscriptions.getAll());
      }

      if (endpoint.includes('/users')) {
        return addMockDelay(mockUsers.getCurrentUser());
      }

      if (endpoint.includes('/dashboard')) {
        if (endpoint.includes('/stats')) return addMockDelay(mockDashboard.getStats());
        if (endpoint.includes('/activity')) return addMockDelay(mockDashboard.getRecentActivity());
        if (endpoint.includes('/vessels')) return addMockDelay(mockDashboard.getVesselSummaries());
        if (endpoint.includes('/sensors')) return addMockDelay(mockDashboard.getSensorSummaries());
      }
    }

    // If no mock data or mock is disabled, make the real API call
    try {
      const url = createUrl(endpoint);
      const response = await fetch(url, {
        method: 'GET',
        headers: createHeaders(options.headers),
      });
      return await handleResponse(response, endpoint);
    } catch (error) {
      return handleApiError(error, endpoint, options.useMockFallback);
    }
  },

  /**
   * Sends a POST request to the API
   */
  post: async (endpoint, data = {}, options = {}) => {
    // If mock fallback is enabled, try to use mock data directly
    if (FEATURES.USE_MOCK_FALLBACK && options.useMockFallback !== false) {
      // Check for specific endpoints to return the appropriate mock data
      if (endpoint.includes('/vessels')) {
        if (endpoint.includes('/list')) {
          return addMockDelay(mockVesselData.getAll());
        }
        if (endpoint.includes('/get')) {
          return addMockDelay(mockVesselData.getById(data.Id || data.id));
        }
        return addMockDelay(mockVesselData.create(data));
      }

      if (endpoint.includes('/sensor')) {
        if (endpoint.includes('/list')) {
          return addMockDelay(mockSensorData.getAll());
        }
        if (endpoint.includes('/get')) {
          return addMockDelay(mockSensorData.getById(data.Id || data.id));
        }
        return addMockDelay(mockSensorData.create(data));
      }

      if (endpoint.includes('/companies')) {
        if (endpoint.includes('/list')) {
          return addMockDelay(mockCompanies.getAll());
        }
        if (endpoint.includes('/get')) {
          return addMockDelay(mockCompanies.getById(data.Id || data.id));
        }
        return addMockDelay(mockCompanies.create(data));
      }

      if (endpoint.includes('/customers')) {
        if (endpoint.includes('/list')) {
          return addMockDelay(mockCustomers.getAll());
        }
        if (endpoint.includes('/get')) {
          return addMockDelay(mockCustomers.getById(data.Id || data.id));
        }
        return addMockDelay(mockCustomers.create(data));
      }

      // Geo-fencing mock fallbacks
      if (endpoint.includes('/vesselfenceevent')) {
        // List vessel-specific fence events
        if (endpoint.includes('/vessel-events')) {
          return addMockDelay(Promise.resolve(mockVesselFenceEvents));
        }
        // List all active fence events
        if (endpoint.includes('/active-events')) {
          return addMockDelay(Promise.resolve(mockActiveFenceEvents));
        }
        // Coordinate check (simple deterministic mock)
        if (endpoint.includes('/check-coordinates')) {
          return addMockDelay(Promise.resolve({ insideFence: false }));
        }
      }
    }

    // If no mock data or mock is disabled, make the real API call
    try {
      const url = createUrl(endpoint);

      // Auth endpoints don't use envelope format
      const requestBody = endpoint.includes('/api/auth/')
        ? JSON.stringify(data)
        : JSON.stringify(createRequestEnvelope(data));

      console.log('ApiClient POST endpoint:', endpoint);
      console.log('ApiClient POST data:', data);
      console.log('ApiClient POST requestBody:', requestBody);

      const headers = createHeaders(options.headers);

      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: requestBody,
      });
      return await handleResponse(response, endpoint);
    } catch (error) {
      return handleApiError(error, endpoint, options.useMockFallback);
    }
  },

  /**
   * Sends a PUT request to the API
   */
  put: async (endpoint, data = {}, options = {}) => {
    // If mock fallback is enabled, try to use mock data directly
    if (FEATURES.USE_MOCK_FALLBACK && options.useMockFallback !== false) {
      // Check for specific endpoints to return the appropriate mock data
      if (endpoint.match(/\/vessels\/[^/]+$/)) {
        const id = endpoint.split('/').pop();
        return mockVesselData.update(id, data);
      }

      if (endpoint.match(/\/companies\/[^/]+$/)) {
        const id = endpoint.split('/').pop();
        return mockCompanies.update(id, data);
      }

      if (endpoint.match(/\/customers\/[^/]+$/)) {
        const id = endpoint.split('/').pop();
        return mockCustomers.update(id, data);
      }

      if (endpoint.includes('/user/profile')) {
        return mockUsers.updateProfile(data);
      }
    }

    // If no mock data or mock is disabled, make the real API call
    try {
      const url = createUrl(endpoint);
      const requestBody = endpoint.includes('/api/auth/')
        ? JSON.stringify(data)
        : JSON.stringify(createRequestEnvelope(data));

      const response = await fetch(url, {
        method: 'PUT',
        headers: createHeaders(options.headers),
        body: requestBody,
      });
      return await handleResponse(response, endpoint);
    } catch (error) {
      return handleApiError(error, endpoint, options.useMockFallback);
    }
  },

  /**
   * Sends a PATCH request to the API
   */
  patch: async (endpoint, data = {}, options = {}) => {
    // If mock fallback is enabled, try to use mock data directly
    if (FEATURES.USE_MOCK_FALLBACK && options.useMockFallback !== false) {
      // For PATCH requests, we'll use the same handlers as PUT for mock data
      if (endpoint.match(/\/vessels\/[^/]+$/)) {
        const id = endpoint.split('/').pop();
        return mockVesselData.update(id, data);
      }

      if (endpoint.match(/\/companies\/[^/]+$/)) {
        const id = endpoint.split('/').pop();
        return mockCompanies.update(id, data);
      }

      if (endpoint.match(/\/customers\/[^/]+$/)) {
        const id = endpoint.split('/').pop();
        return mockCustomers.update(id, data);
      }
    }

    // If no mock data or mock is disabled, make the real API call
    try {
      const url = createUrl(endpoint);
      const requestBody = endpoint.includes('/api/auth/')
        ? JSON.stringify(data)
        : JSON.stringify(createRequestEnvelope(data));

      const response = await fetch(url, {
        method: 'PATCH',
        headers: createHeaders(options.headers),
        body: requestBody,
      });
      return await handleResponse(response, endpoint);
    } catch (error) {
      return handleApiError(error, endpoint, options.useMockFallback);
    }
  },

  /**
   * Sends a DELETE request to the API
   */
  delete: async (endpoint, options = {}) => {
    // If mock fallback is enabled, try to use mock data directly
    if (FEATURES.USE_MOCK_FALLBACK && options.useMockFallback !== false) {
      // Check for specific endpoints to return the appropriate mock data
      if (endpoint.match(/\/vessels\/[^/]+$/)) {
        const id = endpoint.split('/').pop();
        return mockVesselData.delete(id);
      }

      if (endpoint.match(/\/companies\/[^/]+$/)) {
        const id = endpoint.split('/').pop();
        return mockCompanies.delete(id);
      }

      if (endpoint.match(/\/customers\/[^/]+$/)) {
        const id = endpoint.split('/').pop();
        return mockCustomers.delete(id);
      }
    }

    // If no mock data or mock is disabled, make the real API call
    try {
      const url = createUrl(endpoint);
      const response = await fetch(url, {
        method: 'DELETE',
        headers: createHeaders(options.headers),
      });
      return await handleResponse(response, endpoint);
    } catch (error) {
      return handleApiError(error, endpoint, options.useMockFallback);
    }
  },

  /**
   * Gets the current user ID from localStorage/sessionStorage
   * @returns {string|null} User ID or null if not found
   */
  getCurrentUserId() {
    try {
      const savedUser = localStorage.getItem('smartboat_user') || sessionStorage.getItem('smartboat_user');
      if (savedUser) {
        const currentUser = JSON.parse(savedUser);
        return currentUser?.id || null;
      }
    } catch (error) {
      console.warn('Failed to get current user ID:', error);
    }
    return null;
  }
};

export default apiClient;
