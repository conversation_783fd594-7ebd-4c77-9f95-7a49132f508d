/**
 * Owner API Service
 * Handles API requests related to owners
 */

import apiClient from './apiClient';
import { FEATURES } from '../config/envConfig';

const BASE_ENDPOINT = '/api/owner';

const ownerService = {
  /**
   * Get all owners
   * @param {Object} options - Request options including pagination parameters
   * @returns {Promise<Object>} Response with data array and metadata
   */
  getAllOwners: async (options = {}) => {
    try {
      // Extract pagination and filter parameters
      const {
        pageLimit = 25,
        pageOffset = 0,
        sortField = 'name',
        sortOrder = 'ASC',
        searchTerm,
        name,
        status,
        companyId,
        ...requestOptions
      } = options;

      const requestBody = {
        pageLimit,
        pageOffset,
        sortField,
        sortOrder,
        ...(searchTerm && { searchTerm }),
        ...(name && { name }),
        ...(status && { status }),
        ...(companyId && { companyId })
      };

      const response = await apiClient.post(`${BASE_ENDPOINT}/list`, requestBody, {
        ...requestOptions,
        useMockFallback: FEATURES.USE_MOCK_FALLBACK
      });

      // Handle API response format: { data: OwnerDto[], metadata: MetadataDto }
      if (response && response.data && Array.isArray(response.data)) {
        return {
          data: response.data,
          metadata: response.metadata || { pageLimit, pageOffset, total: response.data.length }
        };
      }

      // Handle direct array response (fallback or mock data)
      if (Array.isArray(response)) {
        // Apply client-side pagination for fallback data
        const startIndex = pageOffset;
        const endIndex = startIndex + pageLimit;
        const paginatedData = response.slice(startIndex, endIndex);

        return {
          data: paginatedData,
          metadata: { pageLimit, pageOffset, total: response.length }
        };
      }

      console.warn('Unexpected owner list response format:', response);
      return {
        data: [],
        metadata: { pageLimit, pageOffset, total: 0 }
      };
    } catch (error) {
      console.error('Error fetching owners:', error);
      throw error;
    }
  },

  /**
   * Get an owner by ID
   * @param {string} id - Owner ID (GUID)
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Owner data
   */
  getOwnerById: (id, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/get`, { id }, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Create a new owner
   * @param {Object} ownerData - Owner data (name, email, phone, address, companyId)
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Created owner
   */
  createOwner: (ownerData, options = {}) => {
    const createRequest = {
      name: ownerData.name,
      email: ownerData.email || null,
      phone: ownerData.phone || null,
      address: ownerData.address || null,
      companyId: ownerData.companyId
    };
    return apiClient.post(`${BASE_ENDPOINT}/create`, createRequest, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Update an owner
   * @param {string} id - Owner ID (GUID)
   * @param {Object} ownerData - Updated owner data
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Updated owner
   */
  updateOwner: (id, ownerData, options = {}) => {
    const updateRequest = {
      id,
      name: ownerData.name,
      email: ownerData.email || null,
      phone: ownerData.phone || null,
      address: ownerData.address || null,
      companyId: ownerData.companyId
    };
    return apiClient.post(`${BASE_ENDPOINT}/update`, updateRequest, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Delete an owner
   * @param {string} id - Owner ID (GUID)
   * @param {Object} options - Request options
   * @returns {Promise<boolean>} True if deletion was successful
   */
  deleteOwner: (id, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/delete`, { id }, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Get owners by company
   * @param {string} companyId - Company ID
   * @param {Object} options - Request options
   * @returns {Promise<Array>} Company owners (returns direct array for backward compatibility)
   */
  getOwnersByCompany: async (companyId, options = {}) => {
    console.log('🔍 OwnerService: Fetching owners for company:', companyId);
    console.log('🔍 OwnerService: Options:', options);
    console.log('🔍 OwnerService: USE_MOCK_FALLBACK:', FEATURES.USE_MOCK_FALLBACK);

    try {
      // Use the new getAllOwners method with company filter
      const response = await ownerService.getAllOwners({
        pageLimit: options.pageLimit || 100,
        pageOffset: options.pageOffset || 0,
        companyId: companyId,
        sortField: 'name',
        sortOrder: 'ASC',
        useMockFallback: options.useMockFallback !== false // Default to true unless explicitly false
      });

      console.log('✅ OwnerService: Successfully fetched owners for company:', companyId);
      console.log('✅ OwnerService: Response data length:', response.data?.length || 0);

      // Return just the data array for backward compatibility with existing components
      return response.data || [];
    } catch (error) {
      console.error('❌ OwnerService: Error fetching owners by company:', error);
      console.error('❌ OwnerService: Error details:', {
        message: error.message,
        status: error.status,
        data: error.data
      });
      throw error;
    }
  }
};

export default ownerService;