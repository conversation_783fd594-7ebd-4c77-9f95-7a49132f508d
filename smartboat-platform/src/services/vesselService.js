/**
 * Vessel API Service
 * Handles API requests related to vessels
 */

import apiClient from './apiClient';
import { FEATURES } from '../config/envConfig';

const BASE_ENDPOINT = '/api/vessel';

// VesselType mapping from frontend display names to backend enum values
const VESSEL_TYPE_MAPPING = {
  'Μηχανοκίνητο': 1,  // Mechanical
  'Ιστιοπλοϊκό': 2,   // Sailing
  'Motor-powered': 1,  // English fallback
  'Sailing': 2         // English fallback
};

/**
 * Maps vessel type display name to enum value
 * @param {string|number} type - Vessel type (display name or numeric value)
 * @returns {number} Numeric enum value
 */
const mapVesselType = (type) => {
  // If already numeric, return as-is
  if (typeof type === 'number') {
    return type;
  }

  // If string, try to map to numeric value
  if (typeof type === 'string') {
    return VESSEL_TYPE_MAPPING[type] || 1; // Default to Mechanical (1) if not found
  }

  // Default to Mechanical (1)
  return 1;
};

const vesselService = {
  /**
   * Get all vessels
   * @param {Object} options - Request options including pagination parameters
   * @returns {Promise<Object>} Response with data array and metadata
   */
  getAllVessels: async (options = {}) => {
    try {
      // Extract pagination and filter parameters
      const {
        pageLimit = 25,
        pageOffset = 0,
        sortField = 'name',
        sortOrder = 'ASC',
        searchTerm,
        name,
        number,
        type,
        status,
        companyId,
        ...requestOptions
      } = options;

      const requestBody = {
        pageLimit,
        pageOffset,
        sortField,
        sortOrder,
        ...(searchTerm && { searchTerm }),
        ...(name && { name }),
        ...(number && { number }),
        ...(type && { type }),
        ...(status && { status }),
        ...(companyId && { companyId })
      };

      const response = await apiClient.post(`${BASE_ENDPOINT}/list`, requestBody, {
        ...requestOptions,
        useMockFallback: FEATURES.USE_MOCK_FALLBACK
      });

      // Handle API response format: { data: VesselDto[], metadata: MetadataDto }
      if (response && response.data && Array.isArray(response.data)) {
        // Process vessels to ensure sensors is a number, not an array
        const processedData = response.data.map(vessel => ({
          ...vessel,
          sensors: Array.isArray(vessel.sensors) ? vessel.sensors.length : (vessel.sensors || 0)
        }));

        // Return both data and metadata for pagination
        return {
          data: processedData,
          metadata: response.metadata || { pageLimit, pageOffset, total: processedData.length }
        };
      }

      // Handle direct array response (fallback or mock data)
      if (Array.isArray(response)) {
        // Process vessels to ensure sensors is a number, not an array
        const processedData = response.map(vessel => ({
          ...vessel,
          sensors: Array.isArray(vessel.sensors) ? vessel.sensors.length : (vessel.sensors || 0)
        }));

        // Apply client-side pagination for fallback data
        const startIndex = pageOffset;
        const endIndex = startIndex + pageLimit;
        const paginatedData = processedData.slice(startIndex, endIndex);

        return {
          data: paginatedData,
          metadata: { pageLimit, pageOffset, total: processedData.length }
        };
      }

      console.warn('Unexpected vessel list response format:', response);
      return {
        data: [],
        metadata: { pageLimit, pageOffset, total: 0 }
      };
    } catch (error) {
      console.error('Failed to fetch vessels:', error);
      throw error;
    }
  },

  /**
   * Get a vessel by ID
   * @param {string} id - Vessel ID
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Vessel data
   */
  getVesselById: async (id, options = {}) => {
    try {
      const response = await apiClient.post(`${BASE_ENDPOINT}/get`, { Id: id }, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });

      // Handle API response format
      if (response && response.data) {
        // Preserve sensor array data from API response
        return {
          ...response.data,
          sensors: Array.isArray(response.data.sensors) ? response.data.sensors : (response.data.sensors || [])
        };
      }

      // Handle direct response (fallback or mock data)
      // Preserve sensor array data from direct response
      return {
        ...response,
        sensors: Array.isArray(response.sensors) ? response.sensors : (response.sensors || [])
      };
    } catch (error) {
      console.error('Failed to fetch vessel:', error);
      throw error;
    }
  },

  /**
   * Get vessels by company ID
   * @param {string} companyId - Company ID
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Response with data array and metadata
   */
  getVesselsByCompany: async (companyId, options = {}) => {
    try {
      const response = await apiClient.post(`${BASE_ENDPOINT}/list`, {
        pageLimit: 100,
        pageOffset: 0,
        companyId: companyId,
        sortField: 'name',
        sortOrder: 'ASC'
      }, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });

      // Handle API response format: { data: VesselDto[], metadata: MetadataDto }
      if (response && response.data && Array.isArray(response.data)) {
        const processedData = response.data.map(vessel => ({
          ...vessel,
          sensors: Array.isArray(vessel.sensors) ? vessel.sensors.length : (vessel.sensors || 0)
        }));

        return {
          data: processedData,
          metadata: response.metadata || { pageLimit: 100, pageOffset: 0, total: processedData.length }
        };
      }

      // Handle direct array response (fallback or mock data)
      if (Array.isArray(response)) {
        const processedData = response.map(vessel => ({
          ...vessel,
          sensors: Array.isArray(vessel.sensors) ? vessel.sensors.length : (vessel.sensors || 0)
        }));

        return {
          data: processedData,
          metadata: { pageLimit: 100, pageOffset: 0, total: processedData.length }
        };
      }

      return {
        data: [],
        metadata: { pageLimit: 100, pageOffset: 0, total: 0 }
      };
    } catch (error) {
      console.error('Failed to fetch vessels by company:', error);
      throw error;
    }
  },

  /**
   * Get vessels by owner ID
   * @param {string} ownerId - Owner ID
   * @param {Object} options - Request options
   * @returns {Promise<Array>} List of vessels for the owner
   */
  getVesselsByOwner: async (ownerId, options = {}) => {
    try {
      const response = await apiClient.post(`${BASE_ENDPOINT}/list`, {
        pageLimit: 100,
        pageOffset: 0,
        searchTerm: '', // Filter by owner on backend or client-side
        sortField: 'name',
        sortOrder: 'ASC'
      }, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });

      // Handle API response format: { data: VesselDto[], metadata: MetadataDto }
      let vessels = [];
      if (response && response.data && Array.isArray(response.data)) {
        vessels = response.data;
      } else if (Array.isArray(response)) {
        vessels = response;
      }

      // Filter vessels by owner ID (client-side filtering until backend supports it)
      const ownerVessels = vessels.filter(vessel => vessel.ownerId === ownerId);

      // Process vessels to ensure sensors is a number, not an array
      return ownerVessels.map(vessel => ({
        ...vessel,
        sensors: Array.isArray(vessel.sensors) ? vessel.sensors.length : (vessel.sensors || 0)
      }));
    } catch (error) {
      console.error('Failed to fetch vessels by owner:', error);
      throw error;
    }
  },

  /**
   * Create a new vessel
   * @param {Object} vesselData - Vessel data
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Created vessel
   */
  createVessel: (vesselData, options = {}) => {
    // Map vessel data to ensure proper backend format
    const mappedData = {
      ...vesselData,
      type: mapVesselType(vesselData.type)  // Convert display name to numeric enum value
    };

    return apiClient.post(`${BASE_ENDPOINT}/create`, mappedData, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Update a vessel
   * @param {string} id - Vessel ID
   * @param {Object} vesselData - Updated vessel data
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Updated vessel
   */
  updateVessel: (id, vesselData, options = {}) => {
    // Map vessel data to ensure proper backend format
    const mappedData = {
      ...vesselData,
      id,
      type: vesselData.type ? mapVesselType(vesselData.type) : vesselData.type  // Convert display name to numeric enum value if type is provided
    };

    return apiClient.post(`${BASE_ENDPOINT}/update`, mappedData, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Delete a vessel
   * @param {string} id - Vessel ID
   * @param {Object} options - Request options
   * @returns {Promise<void>}
   */
  deleteVessel: (id, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/delete`, { Id: id }, { ...options, useMockFallback: FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Get vessel path history
   * @param {string} id - Vessel ID
   * @param {Object} params - Query parameters
   * @param {Object} options - Request options
   * @returns {Promise<Array>} Vessel path data
   */
  getVesselPath: (id, params = {}, options = {}) => {
    const queryParams = new URLSearchParams(params).toString();
    // Always use mock data in development mode
    return apiClient.get(`${BASE_ENDPOINT}/${id}/path?${queryParams}`, { ...options, useMockFallback: true });
  },
};

export default vesselService;