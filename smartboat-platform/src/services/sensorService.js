/**
 * Sensor API Service
 * Handles API requests related to sensors
 */

import apiClient from './apiClient';
import { FEATURES } from '../config/envConfig';

const BASE_ENDPOINT = '/api/sensor';

const sensorService = {
  /**
   * Get all sensors
   * @param {Object} options - Request options
   * @param {number} options.pageLimit - Number of items per page (default: 25)
   * @param {number} options.pageOffset - Page offset (default: 0)
   * @param {string} options.sortField - Field to sort by (default: 'name')
   * @param {string} options.sortOrder - Sort order 'ASC' or 'DESC' (default: 'ASC')
   * @param {string} options.searchTerm - Search term for filtering
   * @param {string} options.name - Filter by sensor name
   * @param {string} options.type - Filter by sensor type
   * @param {string} options.vesselId - Filter by vessel ID
   * @param {string} options.status - Filter by sensor status
   * @returns {Promise<Object>} Response with data array and metadata
   */
  getAllSensors: async (options = {}) => {
    try {
      const requestPayload = {
        pageLimit: options.pageLimit || 25,
        pageOffset: options.pageOffset || 0,
        sortField: options.sortField || 'name',
        sortOrder: options.sortOrder || 'ASC',
        ...(options.searchTerm && { searchTerm: options.searchTerm }),
        ...(options.name && { name: options.name }),
        ...(options.type && { type: options.type }),
        ...(options.vesselId && { vesselId: options.vesselId }),
        ...(options.status && { status: options.status })
      };

      const response = await apiClient.post(`${BASE_ENDPOINT}/list`, requestPayload, {
        ...options,
        useMockFallback: options.useMockFallback ?? FEATURES.USE_MOCK_FALLBACK
      });

      // Handle API response format: { data: SensorDto[], metadata: MetadataDto }
      if (response && response.data && Array.isArray(response.data)) {
        const processedData = response.data.map(sensor => ({
          ...sensor,
          id: sensor.id || sensor.Id,
          vesselId: sensor.vesselId || sensor.VesselId || sensor.vessel_id || sensor.VesselID || sensor.shipId || sensor.ShipId || (sensor.vessel ? sensor.vessel.id : null),
          name: sensor.name || sensor.Name || 'Unknown Sensor',
          type: sensor.type || sensor.Type || 'Unknown',
          status: sensor.status || sensor.Status || 'Unknown',
          location: sensor.location || sensor.Location || '',
          lastReading: sensor.lastReading || sensor.LastReading || '',
          lastUpdated: sensor.lastUpdated || sensor.LastUpdated || '',
        }));

        // Return both data and metadata for pagination
        return {
          data: processedData,
          metadata: response.metadata || {
            pageLimit: requestPayload.pageLimit,
            pageOffset: requestPayload.pageOffset,
            total: processedData.length
          }
        };
      }

      // Handle direct array response (fallback or mock data)
      if (Array.isArray(response)) {
        const processedData = response.map(sensor => ({
          ...sensor,
          id: sensor.id || sensor.Id,
          vesselId: sensor.vesselId || sensor.VesselId || sensor.vessel_id || sensor.VesselID || sensor.shipId || sensor.ShipId,
          name: sensor.name || sensor.Name || 'Unknown Sensor',
          type: sensor.type || sensor.Type || 'Unknown',
          status: sensor.status || sensor.Status || 'Unknown',
          location: sensor.location || sensor.Location || '',
          lastReading: sensor.lastReading || sensor.LastReading || '',
          lastUpdated: sensor.lastUpdated || sensor.LastUpdated || '',
        }));

        // Return data with mock metadata for backward compatibility
        return {
          data: processedData,
          metadata: {
            pageLimit: requestPayload.pageLimit,
            pageOffset: requestPayload.pageOffset,
            total: processedData.length
          }
        };
      }

      console.warn('Unexpected sensor list response format:', response);
      return {
        data: [],
        metadata: {
          pageLimit: requestPayload.pageLimit,
          pageOffset: requestPayload.pageOffset,
          total: 0
        }
      };
    } catch (error) {
      console.error('Failed to fetch sensors:', error);
      throw error;
    }
  },

  /**
   * Get a sensor by ID
   * @param {string} id - Sensor ID
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Sensor data
   */
  getSensorById: async (id, options = {}) => {
    console.log('sensorService.getSensorById called with:', { id, options });
    try {
      console.log('Making API call to:', `${BASE_ENDPOINT}/get`);
      console.log('Request payload:', { Id: id });
      const response = await apiClient.post(`${BASE_ENDPOINT}/get`, { Id: id }, { ...options, useMockFallback: options.useMockFallback ?? FEATURES.USE_MOCK_FALLBACK });

      console.log('API response received:', response);

      // Handle API response format
      if (response && response.data) {
        const sensor = response.data;
        console.log('Processing sensor data from response.data:', sensor);
        return {
          ...sensor,
          id: sensor.id || sensor.Id,
          vesselId: sensor.vesselId || sensor.VesselId || sensor.vessel_id || sensor.VesselID || sensor.shipId || sensor.ShipId,
          name: sensor.name || sensor.Name || 'Unknown Sensor',
          type: sensor.type || sensor.Type || 'Unknown',
          status: sensor.status || sensor.Status || 'Unknown',
          location: sensor.location || sensor.Location || '',
          lastReading: sensor.lastReading || sensor.LastReading || '',
          lastUpdated: sensor.lastUpdated || sensor.LastUpdated || '',
        };
      }

      // Handle direct response (fallback or mock data)
      if (response) {
        console.log('Processing direct response:', response);
        return {
          ...response,
          id: response.id || response.Id,
          vesselId: response.vesselId || response.VesselId,
          name: response.name || response.Name || 'Unknown Sensor',
          type: response.type || response.Type || 'Unknown',
          status: response.status || response.Status || 'Unknown',
          location: response.location || response.Location || '',
          lastReading: response.lastReading || response.LastReading || '',
          lastUpdated: response.lastUpdated || response.LastUpdated || '',
        };
      }

      console.log('No valid response data found, returning null');
      return null;
    } catch (error) {
      console.error('Failed to fetch sensor:', error);
      throw error;
    }
  },

  /**
   * Get sensors by vessel ID
   * @param {string} vesselId - Vessel ID
   * @param {Object} options - Request options
   * @returns {Promise<Array>} List of sensors for the vessel
   */
  getSensorsByVessel: async (vesselId, options = {}) => {
    const listRequest = {
      pageLimit: options.pageLimit || 100,
      pageOffset: options.pageOffset || 0,
      sortField: options.sortField || 'name',
      sortOrder: options.sortOrder || 'ASC',
      VesselId: vesselId  // Filter by vessel ID (case-sensitive property name)
    };

    try {
      const response = await apiClient.post(`${BASE_ENDPOINT}/list`, listRequest, {
        ...options,
        useMockFallback: options.useMockFallback ?? FEATURES.USE_MOCK_FALLBACK
      });

      // Handle API response format: { data: SensorDto[], metadata: MetadataDto }
      if (response && response.data && Array.isArray(response.data)) {
        return response.data.map(sensor => ({
          ...sensor,
          id: sensor.id || sensor.Id,
          vesselId: sensor.vesselId || sensor.VesselId || sensor.vessel_id || sensor.VesselID || sensor.shipId || sensor.ShipId || (sensor.vessel ? sensor.vessel.id : null),
          name: sensor.name || sensor.Name || 'Unknown Sensor',
          type: sensor.type || sensor.Type || 'Unknown',
          status: sensor.status || sensor.Status || 'Unknown',
          location: sensor.location || sensor.Location || '',
          lastReading: sensor.lastReading || sensor.LastReading || '',
          lastUpdated: sensor.lastUpdated || sensor.LastUpdated || '',
        }));
      }

      // Handle direct array response (fallback or mock data)
      if (Array.isArray(response)) {
        return response.map(sensor => ({
          ...sensor,
          id: sensor.id || sensor.Id,
          vesselId: sensor.vesselId || sensor.VesselId || sensor.vessel_id || sensor.VesselID || sensor.shipId || sensor.ShipId,
          name: sensor.name || sensor.Name || 'Unknown Sensor',
          type: sensor.type || sensor.Type || 'Unknown',
          status: sensor.status || sensor.Status || 'Unknown',
          location: sensor.location || sensor.Location || '',
          lastReading: sensor.lastReading || sensor.LastReading || '',
          lastUpdated: sensor.lastUpdated || sensor.LastUpdated || '',
        }));
      }

      console.warn('Unexpected vessel sensors response format:', response);
      return [];
    } catch (error) {
      console.error('Error fetching vessel sensors:', error);
      throw error;
    }
  },

  /**
   * Create a new sensor
   * @param {Object} sensorData - Sensor data
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Created sensor
   */
  createSensor: (sensorData, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/create`, sensorData, { ...options, useMockFallback: options.useMockFallback ?? FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Update a sensor
   * @param {string} id - Sensor ID
   * @param {Object} sensorData - Updated sensor data
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Updated sensor
   */
  updateSensor: (id, sensorData, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/update`, { ...sensorData, id }, { ...options, useMockFallback: options.useMockFallback ?? FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Delete a sensor
   * @param {string} id - Sensor ID
   * @param {Object} options - Request options
   * @returns {Promise<void>}
   */
  deleteSensor: (id, options = {}) => {
    return apiClient.post(`${BASE_ENDPOINT}/delete`, { Id: id }, { ...options, useMockFallback: options.useMockFallback ?? FEATURES.USE_MOCK_FALLBACK });
  },

  /**
   * Get sensor readings
   * @param {string} id - Sensor ID
   * @param {Object} params - Query parameters (timeframe, interval, etc.)
   * @param {Object} options - Request options
   * @returns {Promise<Array>} Sensor reading data
   */
  getSensorData: async (id, params = {}, options = {}) => {
    try {
      // Use the new sensordatapoint API endpoint
      const requestPayload = {
        SensorId: id,
        StartTime: params.startTime || null,
        EndTime: params.endTime || null,
        Limit: params.limit || 50,
        OrderBy: params.orderBy || 'Timestamp',
        Descending: params.descending !== false // Default to descending (newest first)
      };

      // Handle timeframe parameter by setting start/end times
      if (params.timeframe && !params.startTime && !params.endTime) {
        const now = new Date();
        const endTime = now.toISOString();
        let startTime;

        switch (params.timeframe) {
          case 'day':
            startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString();
            break;
          case 'week':
            startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
            break;
          case 'month':
            startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString();
            break;
          default:
            startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString();
        }

        requestPayload.StartTime = startTime;
        requestPayload.EndTime = endTime;
      }

      const response = await apiClient.post('/api/sensordatapoint/list', requestPayload, {
        ...options,
        useMockFallback: options.useMockFallback ?? FEATURES.USE_MOCK_FALLBACK
      });

      console.log('Sensor data API response:', response);

      // Extract items from the paginated response (try both cases)
      // Handle SmartBoat API response format: { payload: { items: [...] } }
      const result = response?.payload?.items || response?.Items || response?.items || response || [];
      console.log('Processed sensor data result:', result);

      return result;
    } catch (error) {
      console.warn('Sensor data API call failed, using mock data:', error);
      // Force fallback to mock data when API fails
      if (options.useMockFallback !== false) {
        const { generateSensorData } = await import('../utils/mockData');
        // Generate mock data based on timeframe parameter
        const timeframe = params.timeframe || 'day';
        const hours = timeframe === 'day' ? 24 : timeframe === 'week' ? 168 : 720; // month = 30 days
        const dataPoints = Math.min(hours, 50); // Limit to 50 points for performance

        return generateSensorData(dataPoints, 8);
      }
      throw error;
    }
  },

  /**
   * Get sensor alerts
   * @param {string} id - Sensor ID
   * @param {Object} params - Query parameters
   * @param {Object} options - Request options
   * @returns {Promise<Array>} Sensor alerts
   */
  getSensorAlerts: (id, params = {}, options = {}) => {
    const queryParams = new URLSearchParams(params).toString();
    return apiClient.get(`${BASE_ENDPOINT}/${id}/alerts?${queryParams}`, options);
  },

  /**
   * Update sensor threshold settings
   * @param {string} id - Sensor ID
   * @param {Object} thresholdData - Threshold configuration
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Updated sensor
   */
  updateSensorThresholds: (id, thresholdData, options = {}) => {
    return apiClient.patch(`${BASE_ENDPOINT}/${id}/thresholds`, thresholdData, options);
  },
};

export default sensorService;