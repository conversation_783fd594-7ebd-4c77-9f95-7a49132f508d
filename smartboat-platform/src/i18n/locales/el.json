{"common": {"dashboard": "Πίνα<PERSON><PERSON><PERSON>λ<PERSON>γχου", "vessels": "Σκάφη", "sensors": "Αισθητήρες", "customers": "Πελάτες", "companies": "Εταιρείες", "owners": "Ιδιοκτήτες", "subscriptions": "Συνδρομές", "profile": "<PERSON>ρ<PERSON><PERSON><PERSON><PERSON>", "backToDashboard": "Επιστροφή στον Πίνακα Ελέγχου", "welcome": "Καλώς ήρθατε στο SmartBoat Platform", "logout": "Αποσύνδεση", "loading": "Φόρτωση...", "search": "Αναζήτηση...", "save": "Αποθήκευση", "cancel": "Ακύρωση", "add": "Προσθήκη", "edit": "Επεξεργασία", "delete": "Διαγραφή", "deleting": "Διαγραφή...", "view": "Προβολή", "configure": "Ρύθμιση", "active": "Ενεργό", "inactive": "Ανενεργό", "pending": "Σε αναμονή", "status": "Κατάσταση", "notFound": "Η Σελίδα Δεν Βρέθηκε", "dragToResize": "Σύρετε για αλλαγή μεγέθους", "email": "Email", "phone": "Τηλέφωνο", "name": "Όνομα", "contact": "Επαφή", "contactPerson": "Υπεύθυνος Επικοινωνίας", "actions": "Ενέργειες", "close": "Κλείσιμο", "update": "Ενημέρωση", "create": "Δημιουργία", "required": "* Απαιτείται", "previous": "Προηγούμενο", "next": "Επόμενο", "showing": "Εμφάνιση", "to": "έως", "of": "από", "itemsPerPage": "Στοιχεία ανά σελίδα", "showingItems": "Εμφάνιση {{start}} έως {{end}} από {{total}} στοιχεία", "previousPage": "Προηγούμενη σελίδα", "nextPage": "Επόμενη σελίδα", "goToPage": "Μετάβαση στη σελίδα {{page}}", "all": "Όλα", "download": "Λή<PERSON>η", "noResults": "Δεν βρέθηκαν αποτελέσματα", "last24Hours": "Τελευταίες 24 Ώρες", "last7Days": "Τελευταίες 7 Ημέρες", "last30Days": "Τελευταίες 30 Ημέρες", "customRange": "Προσαρμοσμένο Εύρος", "from": "Από", "selectDate": "Επιλογή ημερομηνίας", "searchPlaceholder": "Αναζήτηση με όνομα, email ή υπεύθυνο επικοινωνίας", "backTo": "Επιστροφή στο", "lastActive": "Τελευτα<PERSON>α ενεργός", "emailAddress": "Διεύθυνση Email", "phoneNumber": "Αριθ<PERSON><PERSON><PERSON> Τ<PERSON>λεφώνου", "statistics": "Στατιστικά", "details": "Λεπτομέρειες", "overview": "Επισκόπηση", "billing": "Τιμολόγηση", "unknown": "Άγνωστο", "blocks": "ΕΝΟΤΗΤΕΣ", "basicInformation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Πληροφορίες", "preview": "Προεπισκόπηση", "fillAllRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> συμπληρώστε όλα τα υποχρεωτικά πεδία", "companyId": "ID Εταιρείας", "id": "ID", "type": "Τύπος", "vessel": "Σκάφος", "location": "Τοποθεσία", "apply": "Εφαρμογή", "saveChanges": "Αποθήκευση Αλλαγών", "role": "Ρόλος", "change": "Αλλαγή", "company": "Εταιρεία", "optional": "Προαιρετικό", "loadingVessels": "Φόρτωση πλοίων..."}, "auth": {"signIn": "Σύνδεση", "emailAddress": "Διεύθυνση email", "password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πρόσβασης", "rememberMe": "Να με θυμάσαι", "forgotPassword": "Ξεχάσατε τον κωδικό σας;", "signingIn": "Σύνδεση...", "demoAccounts": "Λογαριασμοί Επίδειξης", "adminUser": "Διαχειριστής", "customerUser": "Πελάτης", "enterEmailPassword": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε email και κωδικό πρόσβασης.", "invalidCredentials": "Μη έγκυρα διαπιστευτήρια. Π<PERSON>ρακ<PERSON>λ<PERSON> δοκιμάστε ξανά.", "loginError": "Προέκυψε σφάλμα κατά τη σύνδεση. Παρακαλ<PERSON> δοκιμάστε ξανά.", "resetPassword": "Επαναφ<PERSON><PERSON><PERSON> Κωδικού Πρόσβασης", "resetInstructions": "Εάν υπάρχει λογαριασμός με αυτό το email, θα λάβετε οδηγίες για την επαναφορά του κωδικού πρόσβασής σας σύντομα.", "enterEmail": "Εισάγετε τη διεύθυνση email σας", "sendInstructions": "Αποστο<PERSON><PERSON> Οδηγιών Επαναφοράς", "sending": "Αποστολή...", "backToLogin": "Επιστροφή στη Σύνδεση", "sessionTimeout": "Προειδοποίηση λήξης συνεδρίας:", "logoutIn": "Θα αποσυνδεθείτε σε", "stayLoggedIn": "Παραμείνετε Συνδεδεμένοι"}, "dashboard": {"recentActivity": "Πρόσφατη Δραστηριότητα", "expandAll": "Ανάπτυξη όλων", "showAll": "Εμφάνιση όλων", "minAgo": "λεπτά πριν", "hourAgo": "ώρα πριν", "hoursAgo": "ώρες πριν", "newSensorAdded": "<PERSON><PERSON><PERSON> αισθητή<PERSON><PERSON>ς προστέθηκε στο", "subscriptionRenewed": "Η συνδρομή ανανεώθηκε για", "alertAnomalyDetected": "Ειδοποίηση: Εντ<PERSON><PERSON><PERSON>στηκε ανωμαλία στον αισθητήρα θερμοκρασίας του", "temperatureSensor": "αισθητήρα θερμοκρασίας", "vesselSensors": "Αισθητήρες", "statCards": {"customers": "Πελάτες", "companies": "Εταιρείες", "vessels": "Σκάφη", "activeSensors": "Ενεργοί Αισθητήρες"}}, "vessels": {"name": "Όνομα", "number": "Αριθμός", "type": "Τύπος", "location": "Τοποθεσία", "startDate": "Ημερομηνία Έναρξης", "endDate": "Ημερομηνία Λήξης", "onsigners": "Επιβιβάσεις", "offsigners": "Αποβιβάσεις", "lastUpdated": "Τελευταία Ενημέρωση", "details": "Λεπτομέρειες Σκάφους", "vesselInformation": "Πληροφορ<PERSON><PERSON>ς <PERSON>φους", "quickActions": "Γρήγορες Ενέργειες", "noVessels": "Δεν βρέθηκαν σκάφη", "vesselsList": "Σκάφη", "addNewVessel": "Προσθήκη Νέου Σκάφους", "editVessel": "Επεξεργα<PERSON><PERSON><PERSON> Σκάφους", "createNewVessel": "Δημιουρ<PERSON><PERSON><PERSON> Νέου Σκάφους", "searchVessels": "Αναζήτηση σκαφών με όνομα, αριθμ<PERSON> ή τοποθεσία", "noVesselsFound": "Δεν βρέθηκαν σκάφη", "noVesselsMessage": "Προσπαθήστε να αλλάξετε την αναζήτηση ή τα φίλτρα, ή προσθέστε ένα νέο σκάφος.", "noVesselsMessageUser": "Προσπαθήστε να αλλάξετε την αναζήτηση ή τα φίλτρα.", "allStatuses": "Όλες οι Καταστάσεις", "active": "Ενεργό", "maintenance": "Σε Συντήρηση", "inactive": "Ανενεργό", "vesselNumber": "Αριθ<PERSON><PERSON><PERSON>ς", "currentLocation": "Τρέχουσα Τοποθεσία", "totalSensors": "Σύνολ<PERSON>σθητήρων", "backToVessels": "Επιστροφή στα Σκάφη", "vesselNotFound": "Το σκάφος δεν βρέθηκε", "viewDetails": "Προβολ<PERSON> Λεπτομερειών", "manageSensors": "Διαχείριση Αισθητήρων", "updateVessel": "Ενημέρωση Σκάφους", "createVessel": "Δημιουρ<PERSON><PERSON><PERSON> Σκάφους", "updateVesselInfo": "Ενημέρωση πληροφοριών σκάφους", "addNewVesselInfo": "Προσθήκη νέου σκάφους στην πλατφόρμα", "lastUpdate": "Τελευτα<PERSON>α ενημέρωση", "sensorOverview": "Επισκόπηση Αισθητήρων", "vesselSensors": "Αισθητή<PERSON><PERSON><PERSON>ο<PERSON>ς", "scheduleInteractive": "Το διαδραστι<PERSON><PERSON> ημερολόγιο προγραμματισμού θα είναι διαθέσιμο σε μελλοντικές ενημερώσεις", "statUnavailable": "Τα στατιστικά θα είναι διαθέσιμα μετά τη δημιουργία του σκάφους", "tabs": {"vesselInfo": "Πληροφορ<PERSON><PERSON>ς <PERSON>φους", "routeMap": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Διαδρομής", "sensors": "Αισθητήρες"}, "blocks": {"basicInformation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Πληροφορίες", "schedule": "Πρόγραμμα", "scheduleDetails": "Λεπτομέρειες Προγράμματος", "statistics": "Στατιστικά"}, "vesselTypes": {"mechanical": "Μηχανοκ<PERSON>νητο", "sailing": "Ιστιοπλοϊκό"}, "confirmDelete": "Διαγρα<PERSON><PERSON> Σκάφους", "deleteWarning": "Είστε σίγουροι ότι θέλετε να διαγράψετε το σκάφος {{vesselName}}; Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "deleteFailed": "Αποτυχία διαγραφής σκάφους. Παρακ<PERSON><PERSON><PERSON> δοκιμάστε ξανά.", "deleteVessel": "Διαγρα<PERSON><PERSON> Σκάφους", "selectOwner": "Επιλο<PERSON><PERSON>οκτήτη", "noOwnerSelected": "Δεν επιλέχθηκε ιδιοκτήτης", "noOwnersForCompany": "Δεν υπάρχουν διαθέσιμοι ιδιοκτήτες για αυτή την εταιρεία", "ownerHasNoVessels": "Ο/Η {{owner<PERSON><PERSON>}} δεν έχει ανατεθειμένα σκάφη"}, "sensors": {"name": "Όνομα", "type": "Τύπος", "vessel": "Σκάφος", "selectVessel": "Επιλέξτε ένα σκάφος", "location": "Τοποθεσία", "lastReading": "Τελευταία Μέτρηση", "lastUpdated": "Τελευταία Ενημέρωση", "alertThreshold": "Όριο Ειδοποίησης", "readings": "Μετρήσεις", "average": "Μέ<PERSON><PERSON> Όρος", "current": "Τρέχουσα", "minimum": "Ελάχιστη", "maximum": "Μέγιστη", "sensorData": "Δεδομένα Αισθητήρα", "loadingSensorData": "Φόρτωση δεδομένων αισθητήρα...", "fetchingSensorReadings": "Λή<PERSON>η μετρήσεων αισθητήρα...", "errorLoadingSensorData": "Αποτυχ<PERSON>α φόρτωσης δεδομένων αισθητήρα", "noDataAvailable": "Δεν υπάρχουν διαθέσιμα δεδομένα αισθητήρα", "time": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor": "Αισθητήρας", "sensorInformation": "Πληροφορ<PERSON><PERSON>ς Αισθητήρα", "sensorReadings": "Μετρή<PERSON><PERSON><PERSON>ς Αισθητήρα", "quickActions": "Γρήγορες Ενέργειες", "recalibrate": "Επαναβαθμονόμηση", "diagnosticTest": "Διαγνωστικός Έλεγχος", "noSensors": "Δεν βρέθηκαν αισθητήρες", "sensorName": "Όνομα Αισθητήρα", "vesselLocation": "Σκάφος / Τοποθεσία", "addNewSensor": "Προσθήκη Νέου Αισθητήρα", "editSensor": "Επεξεργασία Αισθητήρα", "createNewSensor": "Δημιουρ<PERSON><PERSON>α Νέου Αισθητήρα", "searchSensors": "Αναζήτηση αισθητήρων με όνομα, σκάφος ή τοποθεσία", "noSensorsMessageAdmin": "Προσπαθήστε να αλλάξετε την αναζήτηση ή τα φίλτρα, ή προσθέστε έναν νέο αισθητήρα.", "noSensorsMessageUser": "Προσπαθήστε να αλλάξετε την αναζήτηση ή τα φίλτρα.", "allTypes": "Όλοι οι Τύποι", "allStatuses": "Όλες οι Καταστάσεις", "types": {"temperature": "Θερμοκρασία", "pressure": "Πίεση", "humidity": "Υγρασία", "flowRate": "Ρυθμός Ροής"}, "status": {"warning": "Προειδοποίηση", "critical": "Κρίσιμο"}, "configuration": "Ρύθμιση Παραμέτρων", "blocks": "ΕΝΟΤΗΤΕΣ", "basicInformation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Πληροφορίες", "updateSensorInfo": "Ενημέρωση πληροφοριών αισθητήρα", "addNewSensorInfo": "Προσθήκη νέου αισθητήρα στην πλατφόρμα", "readingsData": "Δεδομένα Μετρήσεων", "readingsUnavailable": "Τα δεδομένα μετρήσεων θα είναι διαθέσιμα μετά τη δημιουργία του αισθητήρα", "historicalReadingsUnavailable": "Το ιστορικό γράφημα μετρήσεων θα είναι διαθέσιμο σε μελλοντικές ενημερώσεις", "updateSensor": "Ενημέρωση Αισθητήρα", "createSensor": "Δημιουργ<PERSON>α Αισθητήρα", "fillAllRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> συμπληρώστε όλα τα υποχρεωτικά πεδία", "sensorNotFound": "Ο αισθητήρας δεν βρέθηκε", "backToSensors": "Επιστροφή στους Αισθητήρες", "history": "Ιστορικό", "settings": "Ρυθμίσεις", "readingHistory": "Ιστορικ<PERSON> Μετρήσεων", "dateRange": "<PERSON><PERSON><PERSON><PERSON>ν", "resolution": "Ανάλυση", "hourly": "Ωριαία", "daily": "Ημερήσια", "weekly": "Εβδομαδιαία", "last24Hours": "Τελευταίες 24 ώρες", "last7Days": "Τελευταίες 7 ημέρες", "last30Days": "Τελευταίες 30 ημέρες", "customRange": "Προσαρμοσμένο εύρος", "timestamp": "Χρονική Σήμανση", "value": "Τιμή", "sensorSettings": "Ρυθμίσεις Αισθητήρα", "generalSettings": "Γενικές Ρυθμίσεις", "alertConfiguration": "Ρύθμιση Ειδοποιήσεων", "samplingRate": "Ρυθμός Δειγματοληψίας", "every5Minutes": "Κάθε 5 λεπτά", "every15Minutes": "Κάθε 15 λεπτά", "every30Minutes": "Κάθε 30 λεπτά", "enableEmailAlerts": "Ενεργοποίηση ειδοποιήσεων μέσω email", "enableSmsAlerts": "Ενεργοποίηση ειδοποιήσεων μέσω SMS", "confirmDelete": "Διαγρα<PERSON><PERSON> Αισθητήρα", "deleteWarning": "Είστε σίγουροι ότι θέλετε να διαγράψετε τον αισθητήρα {{sensorName}}; Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "deleteFailed": "Αποτυχία διαγραφής αισθητήρα. Παρακ<PERSON>λ<PERSON> δοκιμάστε ξανά.", "deleteSensor": "Διαγρα<PERSON><PERSON> Αισθητήρα", "errorLoadingSensors": "Αποτυχ<PERSON>α φόρτωσης αισθητήρων"}, "subscriptions": {"yourSubscriptions": "Οι Συνδρομές σας", "yourCurrentSubscription": "Η Τρέχουσα Συνδρομή σας", "yourOtherSubscriptions": "Οι Άλλες Συνδρομές σας", "manageSubscriptions": "Διαχείριση Συνδρομών", "addNewSubscription": "Προσθήκη Νέας Συνδρομής", "createNewSubscription": "Δημιουρ<PERSON><PERSON><PERSON> Νέας Συνδρομής", "editSubscription": "Επεξεργασία Συνδρομής", "price": "Τιμή", "validFrom": "Ισχύει από", "validTo": "έως", "plan": "Πλάν<PERSON>", "noSubscriptionsFound": "Δεν Βρέθηκαν Συνδρομές", "noSubscriptionsMessage": "Δεν υπάρχουν ενεργές συνδρομές στο σύστημα. Κάντε κλικ στο παρακάτω κουμπί για να δημιουργήσετε μια νέα συνδρομή.", "billingFrequency": "Συχνότητα Χρέωσης", "startDate": "Ημερομηνία Έναρξης", "endDate": "Ημερομηνία Λήξης", "monthly": "Μηνιαία", "quarterly": "Τριμηνιαία", "yearly": "Ετήσια", "status": {"active": "Ενεργή", "pending": "Σε Αναμονή", "expired": "Έληξε", "cancelled": "Ακυρώθηκε"}, "subscriptionPeriod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ρομής", "subscriptionNotFound": "Η συνδρομή δεν βρέθηκε", "subscriptionNotFoundMessage": "Η συνδρομή που αναζητάτε δεν υπάρχει ή δεν έχετε άδεια να τη δείτε.", "backToSubscriptions": "Επιστροφή στις Συνδρομές", "errorLoadingSubscription": "Σφάλ<PERSON>α κατά τη φόρτωση της συνδρομής", "features": "<PERSON><PERSON><PERSON><PERSON><PERSON>τηριστικά", "paymentInformation": "Πληροφορίες Πληρωμής", "nextBillingDate": "Επόμενη ημερομηνία χρέωσης", "paymentMethod": "Τρόπος πληρωμής", "updateSubscriptionDetails": "Ενημέρωση λεπτομερειών συνδρομής", "addNewSubscriptionInfo": "Προσθήκη νέου πλάνου συνδρομής για πελάτη", "billingDetails": "Λεπτομέρειες Χρέωσης", "planFeatures": "Χ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>λάνου", "subscriptionName": "Όνομα Συνδρομής", "subscriptionType": "<PERSON><PERSON><PERSON><PERSON>υνδρομής", "selectCustomer": "Επιλογ<PERSON>ελάτ<PERSON>", "selectVessel": "Επιλογή Πλοίου", "errorFetchingVessels": "Αποτυχ<PERSON><PERSON> φόρτωσης πλοίων. Παρακαλ<PERSON> δοκιμάστε ξανά.", "pricePerSensor": "Τιμή Ανά Αισθητήρα", "sensorLimit": "Όριο Αισθητήρων ανά Σκάφος", "unlimitedSensorsInfo": "Αφήστε κενό για απεριόριστους αισθητήρες", "featuresPerLine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>τι<PERSON><PERSON> (έν<PERSON> <PERSON>νά γραμμή)", "enterFeaturesPlaceholder": "Εισάγετε χαρακτη<PERSON><PERSON><PERSON><PERSON>ι<PERSON><PERSON>, έν<PERSON> ανά γραμμή", "featuresInstructionText": "Καταγράψτε τα χαρακτηριστι<PERSON><PERSON> που περιλαμβάνονται σε αυτό το πλάνο συνδρομής, με κάθε χαρακτηριστικό σε νέα γραμμή.", "updateSubscription": "Ενημέρωση Συνδρομής", "createSubscription": "Δημιουργ<PERSON><PERSON> Συνδρομής", "viewDetails": "Προβολ<PERSON> Λεπτομερειών", "planTypes": {"standard": "Βασικό", "professional": "Επαγγελματικό", "enterprise": "Επιχειρηματικό", "custom": "Προσαρμοσμένο"}}, "notFound": {"title": "404", "subtitle": "Η Σελίδα Δεν Βρέθηκε", "message": "Η σελίδα δεν υπάρχει ή δεν έχετε πρόσβαση σε αυτήν.", "backToDashboard": "Μετάβαση στον Πίνακα Ελέγχου", "goBack": "Επιστροφή"}, "profile": {"userProfile": "<PERSON>ρ<PERSON><PERSON><PERSON><PERSON>", "personalInformation": "Προσω<PERSON><PERSON><PERSON><PERSON>ς Πληροφορίες", "accountSettings": "Ρυθμίσεις Λογαριασμού", "notifications": "Ειδοποιήσεις", "language": "Γλώσσα", "english": "Αγγλικ<PERSON>", "greek": "Ελληνικά", "french": "Γαλλικά", "changePassword": "Αλλαγ<PERSON> Κωδικού Πρόσβασης", "currentPassword": "Τρέχων <PERSON>δικ<PERSON>ς", "newPassword": "<PERSON><PERSON><PERSON>", "confirmPassword": "Επιβεβαίωση Κωδικού", "saveChanges": "Αποθήκευση Αλλαγών", "editProfile": "Επεξεργασία Προφίλ", "joined": "Εγγραφή", "lastLogin": "Τελευτα<PERSON>α σύνδεση", "fullName": "Ονοματεπώνυμο", "bio": "Βιογραφικό", "workInformation": "Πληροφορίες Εργασίας", "company": "Εταιρεία", "department": "Τμήμα", "socialLinks": "Κοινωνικά Δίκτυα", "linkedinUrl": "Διεύθυνση LinkedIn", "twitterUrl": "Διεύθυνση Twitter", "githubUrl": "Διεύθυνση GitHub", "notProvided": "Δεν παρέχεται", "securitySettings": "Ρυθμίσεις Ασφάλειας", "twoFactorAuth": "Έλεγχος Ταυτότητας Δύο Παραγόντων", "twoFactorStatus": "Ο έλεγχος ταυτότητας δύο παραγόντων είναι", "enabled": "ενεργοποιημένος", "notEnabled": "απενεργοποιημένος", "forYourAccount": "για το λογαριασμό σας", "enable": "Ενεργοποίηση", "disable": "Απενεργοποίηση", "changeYourPassword": "Αλλάξτε τον κωδικό πρόσβασής σας", "updatePassword": "Ενημέρωση Κωδικού", "loginSessions": "Συνεδρίες Σύνδεσης", "currentSession": "Τρέχουσα Συνεδρία", "started": "Ξεκίνησε", "signOutAllSessions": "Αποσύνδεση από όλες τις άλλες συνεδρίες", "accountActions": "Ενέργειες Λογαριασμού", "deactivateAccount": "Απενεργοποίηση λογαριασμού", "languageSetEnglish": "Η γλώσσα της εφαρμογής έχει οριστεί στα Αγγλικά", "languageSetGreek": "Η γλώσσα της εφαρμογής έχει οριστεί στα Ελληνικά", "languageSetFrench": "Η γλώσσα της εφαρμογής έχει οριστεί στα Γαλλικά", "change": "Αλλαγή", "role": "Ρόλος"}, "customers": {"customersList": "Πελάτες", "addNewCustomer": "Προσθήκη Νέου Πελάτη", "editCustomer": "Επεξεργασία Πελάτη", "createNewCustomer": "Δημιουρ<PERSON><PERSON><PERSON> Νέου Πελάτη", "searchCustomers": "Αναζήτηση πελατών με όνομα, υπεύθυνο επικοινωνίας ή email", "noCustomersFound": "Δεν βρέθηκαν πελάτες", "noCustomersMessage": "Προσπαθήστε να αλλάξετε τα κριτήρια αναζήτησης ή προσθέστε έναν νέο πελάτη.", "customerDetails": "Λεπτομέρειες <PERSON>η", "companyInformation": "Πληροφορίες Εταιρείας", "primaryContact": "Κύρια Επαφή", "subscriptionOverview": "Επισκόπηση Συνδρομής", "viewVessels": "Προβολ<PERSON> Σκαφών", "backToCustomers": "Επιστροφή στους Πελάτες", "customerName": "Όνομα Πελάτη", "fillAllRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> συμπληρώστε όλα τα υποχρεωτικά πεδία", "basicInformation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Πληροφορίες", "contactDetails": "Στοιχεία Επικοινωνίας", "updateCustomerInfo": "Ενημέρωση πληροφοριών πελάτη", "addNewCustomerInfo": "Προσθήκη νέου πελάτη στην πλατφόρμα", "blocks": "ΕΝΟΤΗΤΕΣ", "statUnavailable": "Τα στατιστικά θα είναι διαθέσιμα μετά τη δημιουργία του πελάτη", "updateCustomer": "Ενημέρωση Πελάτη", "createCustomer": "Δημιουρ<PERSON><PERSON><PERSON>λ<PERSON>τη", "customerNotFound": "Ο πελάτης δεν βρέθηκε", "deleteCustomer": "Διαγρα<PERSON><PERSON> Πελάτη", "confirmDelete": "Επιβεβαίωση Διαγραφής", "deleteWarning": "Είστε σίγουροι ότι θέλετε να διαγράψετε τον πελάτη {{customerName}}; Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "deleteFailed": "Η διαγραφή του πελάτη απέτυχε. Παρακαλ<PERSON> δοκιμάστε ξανά.", "recentActivity": "Πρόσφατη Δραστηριότητα", "subscription": {"currentPlan": "Τρέχον Πλάνο", "changePlan": "Αλλαγ<PERSON>λάνου", "enterprise": "Enterprise", "unlimited": "Απεριόριστα", "allowed": "επιτρεπόμενα", "billingInformation": "Πληροφορ<PERSON>ες Τιμολόγησης", "updatePaymentMethod": "Ενημέρωση Τρόπου Πληρωμής", "billingHistory": "Ιστορικ<PERSON> Τιμολόγησης", "invoiceNumber": "Αριθμός Τιμολογίου", "date": "Ημερομηνία", "amount": "Ποσό", "paid": "Εξοφλημένο", "subscriptionAndBilling": "Συνδρομή & Τιμολόγηση", "errorLoading": "Σφάλ<PERSON>α κατά τη φόρτωση συνδρομών", "sensorLimit": "όριο αισθητήρων", "noSubscriptions": "Δεν υπάρχουν συνδρομές", "subscriptionDetails": "Λεπτομέρειες Συνδρομής", "noSubscriptionDetails": "Δεν υπάρχουν λεπτομέρειες συνδρομής"}, "tabs": {"overview": "Επισκόπηση", "companies": "Εταιρείες", "vessels": "Σκάφη", "subscriptionBilling": "Συνδρομή & Τιμολόγηση"}, "companies": {"addCompany": "Προσθήκη Εταιρείας", "noCompanies": "Δεν υπάρχουν εταιρείες", "noCompaniesMessage": "Ξεκινήστε προσθέτοντας μια εταιρεία σε αυτόν τον πελάτη."}, "vessels": {"allVessels": "Όλα τα Σκάφη (σε όλες τις εταιρείες)", "addVesselToCompany": "Προσθήκη Σκάφους σε Εταιρεία", "unknownCompany": "Άγνωστη Εταιρεία", "noVesselsFound": "Δεν βρέθηκαν σκάφη", "noVesselsMessage": "Καμία από τις εταιρείες αυτού του πελάτη δεν έχει ακόμη σκάφη.", "needCompanyFirst": "Προσθέστε πρώτα μια εταιρεία", "needCompanyMessage": "Πρέπει να προσθέσετε μια εταιρεία πριν μπορέσετε να προσθέσετε σκάφη.", "addCompanyFirst": "Προσθήκη Εταιρείας Πρώτα", "addVesselFirst": "Προσθήκη Σκάφους Πρώτα"}, "errors": {"duplicateName": "Υπάρχει ήδη πελάτης με αυτό το όνομα. Παρα<PERSON><PERSON><PERSON><PERSON> επιλέξτε διαφορετικό όνομα.", "duplicateEmail": "Αυτή η διεύθυνση email χρησιμοποιείται ήδη από άλλον πελάτη. Παρακαλώ χρησιμοποιήστε διαφορετική διεύθυνση email.", "nameAlreadyExists": "Αυτό το όνομα πελάτη είναι ήδη κατειλημμένο. Παρακαλώ χρησιμοποιήστε διαφορετικό όνομα.", "technicalError": "Παρου<PERSON><PERSON>άστηκε τεχνικό σφάλμα κατά την επεξεργασία του αιτήματός σας. Παρακαλ<PERSON> δοκιμάστε ξανά αργότερα.", "validationFailed": "Οι παρεχόμενες πληροφορίες είναι άκυρες. Πα<PERSON>α<PERSON><PERSON><PERSON><PERSON> ελέγξτε την εισαγωγή σας και δοκιμάστε ξανά.", "operationFailed": "Η λειτουργία απέτυχε. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> δοκιμάστε ξανά ή επικοινωνήστε με την υποστήριξη εάν το πρόβλημα συνεχιστεί.", "nameRequired": "Το όνομα του πελάτη είναι υποχρεωτικό.", "nameTooShort": "Το όνομα του πελάτη πρέπει να έχει τουλάχιστον 2 χαρακτήρες."}}, "companies": {"companiesList": "Εταιρείες", "addNewCompany": "Προσθήκη Νέας Εταιρείας", "editCompany": "Επεξεργασία Εταιρείας", "createNewCompany": "Δημιουργ<PERSON><PERSON> Νέας Εταιρείας", "searchCompanies": "Αναζήτηση εταιρειών με όνομα ή τοποθεσία", "noCompaniesFound": "Δεν βρέθηκαν εταιρείες", "noCompaniesMessage": "Προσπαθήστε να αλλάξετε την αναζήτηση ή τα φίλτρα, ή προσθέστε μια νέα εταιρεία.", "noCompaniesMessageUser": "Προσπαθήστε να αλλάξετε την αναζήτηση ή τα φίλτρα για να βρείτε εταιρείες.", "companyInformation": "Πληροφορίες Εταιρείας", "companyDetails": "Λεπτομέρειες Εταιρείας", "companyName": "Όνομα Εταιρείας", "industry": "Κλ<PERSON><PERSON><PERSON>", "location": "Τοποθεσία", "locationDetails": "Λεπτομέρειες Τοποθεσίας", "updateCompanyInfo": "Ενημέρωση πληροφοριών εταιρείας", "addNewCompanyInfo": "Προσθήκη νέας εταιρείας στην πλατφόρμα", "statUnavailable": "Τα στατιστικά θα είναι διαθέσιμα μετά τη δημιουργία της εταιρείας", "updateCompany": "Ενημέρωση Εταιρείας", "createCompany": "Δημιουργία Εταιρείας", "companyNotFound": "Η εταιρεία δεν βρέθηκε", "backToCompanies": "Επιστροφή στις Εταιρείες", "deleteCompany": "Διαγραφή Εταιρείας", "confirmDelete": "Επιβεβαίωση Διαγραφής", "deleteWarning": "Είστε σίγουροι ότι θέλετε να διαγράψετε την {{companyName}}; Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "deleteFailed": "Η διαγραφή της εταιρείας απέτυχε. Παρακαλ<PERSON> δοκιμάστε ξανά.", "companyVessels": "Σκάφη Εταιρείας", "noVesselsFound": "Δεν βρέθηκαν σκάφη", "noVesselsMessage": "Αυτή η εταιρεία δεν έχει ακόμη σκάφη.", "addNewVessel": "Προσθήκη Νέου Σκάφους", "companyOwners": "Ιδιοκτήτες Εταιρείας", "addNewOwner": "Προσθήκη Νέου Ιδιοκτήτη", "noOwnersFound": "Δεν βρέθηκαν ιδιοκτήτες", "noOwnersMessage": "Αυτή η εταιρεία δεν έχει ιδιοκτήτες ακόμη.", "companyPerformance": "Επίδοση Εταιρείας", "quickActions": "Γρήγορες Ενέργειες", "generateReport": "Δημιουργ<PERSON><PERSON> Αναφοράς", "tabs": {"overview": "Επισκόπηση", "vessels": "Σκάφη", "owners": "Ιδιοκτήτες", "contracts": "Συμβόλαια"}, "stats": {"activeVessels": "Ενεργ<PERSON> Σκάφ<PERSON>", "totalVessels": "Σύν<PERSON><PERSON><PERSON>", "ofFleet": "του στόλου", "inFleet": "στον στόλο", "sensorAlertRate": "Ποσοστ<PERSON> Ειδοποιήσεων Αισθητήρων", "onTimePerformance": "Επίδοση Έγκαιρης Άφιξης", "maintenanceRate": "Ποσοστ<PERSON>υντήρησης", "fromLastWeek": "από την προηγούμενη εβδομάδα", "fromLastMonth": "από τον προηγούμενο μήνα", "fromTarget": "από τον στόχο"}, "contracts": {"contracts": "Συμβόλαια", "addNewContract": "Προσθήκη Νέου Συμβολαίου", "validUntil": "Ισχύει έως", "active": "Ενεργό", "renewing": "Σε ανανέωση"}, "nameRequired": "Το όνομα της εταιρείας είναι υποχρεωτικό", "createFailed": "Η δημιουργία της εταιρείας απέτυχε. Παρακα<PERSON><PERSON> δοκιμάστε ξανά.", "selectCustomer": "Επιλογ<PERSON>ελάτ<PERSON>", "assignToCustomer": "Ανάθεση σε Πελάτη", "customerRequired": "Η επιλογή πελάτη είναι υποχρεωτική", "assignedCustomer": "Ανατεθε<PERSON>ς <PERSON>της"}, "owners": {"ownersList": "Ιδιοκτήτες", "addNewOwner": "Προσθήκη Νέου Ιδιοκτήτη", "editOwner": "Επεξεργα<PERSON><PERSON><PERSON> Ιδιοκτήτη", "createNewOwner": "Δημιουρ<PERSON><PERSON><PERSON> Νέου Ιδιοκτήτη", "searchOwners": "Αναζήτη<PERSON>η ιδιοκτητών με όνομα, email ή τηλέφωνο", "noOwnersFound": "Δεν βρέθηκαν ιδιοκτήτες", "noOwnersMessage": "Προσπαθήστε να αλλάξετε την αναζήτηση ή τα φίλτρα, ή προσθέστε έναν νέο ιδιοκτήτη.", "noOwnersMessageUser": "Προσπαθήστε να αλλάξετε την αναζήτηση ή τα φίλτρα για να βρείτε ιδιοκτήτες.", "ownerInformation": "Πληροφο<PERSON><PERSON><PERSON><PERSON> Ιδιοκτήτη", "ownerDetails": "Λεπτομέ<PERSON><PERSON><PERSON><PERSON>ς Ιδιοκτήτη", "ownerName": "Όνομα Ιδιοκτήτη", "basicInformation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Πληροφορίες", "contactDetails": "Στοιχεία Επικοινωνίας", "companyAssignment": "Ανάθεση Εταιρείας", "email": "Email", "phone": "Τηλέφωνο", "address": "Διεύθυνση", "company": "Εταιρεία", "updateOwnerInfo": "Ενημέρωση πληροφοριών ιδιοκτήτη", "addNewOwnerInfo": "Προσθήκη νέου ιδιοκτήτη στην πλατφόρμα", "updateOwner": "Ενημέρωση Ιδιοκτήτη", "createOwner": "Δημιου<PERSON><PERSON><PERSON><PERSON> Ιδιοκτήτη", "ownerNotFound": "Ο ιδιοκτήτης δεν βρέθηκε", "backToOwners": "Επιστροφή στους Ιδιοκτήτες", "deleteOwner": "Διαγ<PERSON>α<PERSON><PERSON>διοκτήτη", "confirmDelete": "Επιβεβαίωση Διαγραφής", "deleteWarning": "Είστε σίγουροι ότι θέλετε να διαγράψετε τον {{ownerName}}; Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "deleteFailed": "Η διαγραφή του ιδιοκτήτη απέτυχε. Παρακαλ<PERSON> δοκιμάστε ξανά.", "createFailed": "Η δημιουργία του ιδιοκτήτη απέτυχε. Παρακ<PERSON><PERSON><PERSON> δοκιμάστε ξανά.", "updateFailed": "Η ενημέρωση του ιδιοκτήτη απέτυχε. Παρακαλ<PERSON> δοκιμάστε ξανά.", "nameRequired": "Το όνομα του ιδιοκτήτη είναι υποχρεωτικό", "companyRequired": "Η επιλογή εταιρείας είναι υποχρεωτική", "selectCompany": "Επιλογή Εταιρείας", "selectOwner": "Επιλέξτε Ιδιοκτήτη", "noOwnerSelected": "Δεν επιλέχθηκε ιδιοκτήτης", "noOwnersForCompany": "Δεν βρέθηκαν ιδιοκτήτες για αυτή την εταιρεία", "assignToCompany": "Ανάθεση σε Εταιρεία", "assignedCompany": "Ανατεθείσα Εταιρεία", "selectedCompany": "Επιλεγμένη Εταιρεία", "enterOwnerName": "Εισάγετε όνομα ιδιοκτήτη", "enterEmail": "Εισάγετε διεύθυνση email", "enterPhone": "Εισάγετε αριθμό τηλεφώνου", "enterAddress": "Εισάγετε διεύθυνση"}, "notifications": {"title": "Ειδοποιήσεις", "noNotifications": "Καμία ειδοποίηση", "noNotificationsMessage": "Δεν έχετε ειδοποιήσεις αυτή τη στιγμή.", "markAllRead": "Σημείωση όλων ως αναγνωσμένα", "markingAllRead": "Σημείωση όλων ως αναγνωσμένα...", "viewAll": "Προβολή όλων των ειδοποιήσεων", "justNow": "μόλις τώρα", "minutesAgo": "{{count}} λεπτά πριν", "hoursAgo": "{{count}} ώρες πριν", "daysAgo": "{{count}} ημέρες πριν", "unreadCount": "{{count}} μη αναγνωσμένα από {{total}} συνολικά", "unreadCountOnly": "{{count}} μη αναγνωσμένα", "allRead": "Όλες οι ειδοποιήσεις έχουν διαβαστεί", "types": {"info": "Πληροφορίες", "success": "Επιτυχία", "warning": "Προειδοποίηση", "error": "Σφάλμα", "alert": "Συναγ<PERSON><PERSON><PERSON><PERSON>ς"}, "categories": {"system": "Σύστημα", "sensor": "Αισθητήρας", "vessel": "Πλοίο", "subscription": "Συνδρομή", "maintenance": "Συντήρηση"}}, "emailProcessing": {"title": "Επεξεργασία Email", "microsoftLogin": "Σύνδεση Microsoft", "tokenStatus": "Κατάστα<PERSON>η Token", "lastSync": "Τελ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>νισμ<PERSON>ς", "nextSync": "Επόμ<PERSON><PERSON><PERSON>ρονισμός", "connectToMicrosoft": "Σύνδεση με Microsoft", "connected": "Συνδεδεμένο", "notConnected": "Μη Συνδεδεμένο", "tokenExpires": "Το token λήγει", "reAuthRequired": "Απαιτείτ<PERSON>ι επανασύνδεση", "syncSuccessful": "Επιτυχής", "syncFailed": "Αποτυχία", "syncPending": "Σε αναμονή", "never": "Ποτ<PERSON>"}, "wlpTemplates": {"title": "Πρότυπα WLP", "description": "Διαχειριστείτε τα αρχεία προτύπων γεοφράκτη Wialon για ανάλυση πλοίων", "uploadTemplate": "Ανέβασμα Προτύπου", "uploadNewTemplate": "Ανέβασμα Νέου Προτύπου", "templateName": "Όνομα Προτύπου", "templateNamePlaceholder": "Εισάγετε όνομα προτύπου", "templateDescription": "Περιγραφή", "templateDescriptionPlaceholder": "Εισάγετε προαιρετική περιγραφή", "selectWlpFile": "Επιλέξτε Αρχείο WLP", "fileRequirements": "Υποστηρίζονται μόνο αρχεία .wlp, μέγιστο 10MB", "existingTemplates": "Υπάρχοντα Πρότυπα", "noTemplates": "<PERSON><PERSON><PERSON><PERSON><PERSON>ότυπα", "noTemplatesDescription": "Ανεβάστε το πρώτο σας πρότυπο WLP για να ξεκινήσετε με την ανάλυση γεοφράκτη", "zones": "ζώνες", "fileInfo": "Πληροφορίες Αρχείου", "uploaded": "Ανεβάστηκε", "by": "από", "confirmDelete": "Είστε σίγουροι ότι θέλετε να διαγράψετε το '{{name}}'; Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "nameRequired": "Το όνομα προτύπου είναι υποχρεωτικό", "invalidFileType": "Παρακ<PERSON><PERSON><PERSON> επιλέξτε έγκυρο αρχείο .wlp", "fileTooLarge": "Το μέγεθος αρχείου πρέπει να είναι μικρότερο από 10MB", "fileReadError": "Σφάλμα ανάγνωσης αρχείου. Π<PERSON>ρακ<PERSON><PERSON><PERSON> δοκιμάστε ξανά.", "uploadSuccess": "Το πρότυπο ανεβάστηκε με επιτυχία", "uploadError": "Αποτυχ<PERSON><PERSON> ανεβάσματος προτύπου. Παρακ<PERSON><PERSON><PERSON> δοκιμάστε ξανά.", "deleteSuccess": "Το πρότυπο διαγράφηκε με επιτυχία", "deleteError": "Αποτυχία διαγραφής προτύπου. Παρακ<PERSON>λ<PERSON> δοκιμάστε ξανά.", "loadError": "Αποτυχ<PERSON><PERSON> φόρτωσης προτύπων. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ανανεώστε τη σελίδα.", "uploadingTemplate": "Ανέβασμα προτύπου..."}, "geofenceAnalysis": {"title": "Ανάλυση Γεωφράκτη", "description": "Αναλύστε τις κινήσεις του πλοίου έναντι προτύπων γεωφράκτη WLP για να εντοπίσετε διασχίσεις ορίων και γεγονότα συμμόρφωσης", "newAnalysis": "Νέα Ανάλυση", "createAnalysis": "Δημιουργ<PERSON>α Ανάλυσης", "creatingAnalysis": "Δημιουργία Ανάλυσης...", "analysisName": "Όνομα Ανάλυσης", "analysisNamePlaceholder": "Προαιρε<PERSON>ι<PERSON><PERSON> όνομα για αυτή την ανάλυση", "selectVessel": "Επιλέξτε Πλοίο", "chooseVessel": "Επιλέξτε ένα πλοίο...", "selectTemplates": "Επιλέξτε Πρότυπα WLP", "dateFrom": "Ημερομηνία Από", "dateTo": "Ημερομηνία Έως", "analysisJobs": "Εργασί<PERSON>ς Ανάλυσης", "noAnalysisJobs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς Ανάλυσης", "noAnalysisJobsDescription": "Δημιουργήστε την πρώτη σας ανάλυση γεωφράκτη για να ξεκινήσετε", "noTemplatesAvailable": "Δεν υπάρχουν διαθέσιμα πρότυπα WLP. Παρα<PERSON><PERSON><PERSON><PERSON> ανεβάστε πρότυπα στο προφίλ σας πρώτα.", "vessel": "Πλοίο", "templates": "Πρότυπα", "dateRange": "<PERSON><PERSON><PERSON><PERSON>ν", "status": "Κατάσταση", "results": "Αποτελέσματα", "created": "Δημιουργήθηκε", "coordinates": "συντεταγμένες", "events": "γεγονότα", "to": "έως", "vesselRequired": "Παρα<PERSON><PERSON><PERSON><PERSON> επιλέξτε ένα πλοίο", "templatesRequired": "Παρα<PERSON><PERSON><PERSON><PERSON> επιλέξτε τουλάχιστον ένα πρότυπο WLP", "datesRequired": "Παρα<PERSON><PERSON><PERSON><PERSON> επιλέξτε εύρος ημερομηνιών", "invalidDateRange": "Η ημερομηνία λήξης πρέπει να είναι μετά την ημερομηνία έναρξης", "createSuccess": "Η εργασία ανάλυσης δημιουργήθηκε με επιτυχία", "createError": "Αποτυχία δημιουργ<PERSON>ας εργασίας ανάλυσης. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> δοκιμάστε ξανά.", "jobCompleted": "Η ανάλυση ολοκληρώθηκε με επιτυχία", "jobFailed": "Η ανάλυση απέτυχε με σφάλματα", "jobRunning": "Η ανάλυση εκτελείται αυτή τη στιγμή", "jobPending": "Η ανάλυση περιμένει να ξεκινήσει", "viewResults": "Προβολή Αποτελεσμάτων", "downloadResults": "Λήψη Αποτελεσμάτων", "cancelJob": "Ακύρωση Εργασίας", "deleteJob": "Διαγραφή Εργασίας"}, "geoFencing": {"title": "Γεγονότα Γεω-περίφραξης", "description": "Παρακολουθήστε πότε αυτό το πλοίο εξέρχεται και επιστρέφει στη γεω-περίφραξη", "error": {"title": "Σφάλμ<PERSON> Φόρτω<PERSON>ης <PERSON>εγονότων Περίφραξης", "message": "Αποτυχ<PERSON><PERSON> φόρτωσης γεγονότων περίφραξης πλοίου. Παρακαλ<PERSON> δοκιμάστε ξανά αργότερα."}, "empty": {"title": "Κανέν<PERSON>γον<PERSON>ς Περίφραξης", "message": "Αυτό το πλοίο δεν έχει εξέλθει από τη γεω-περίφραξη ακόμα.", "filtered": "Δεν βρέθηκαν γεγονότα με το επιλεγμένο φίλτρο.", "boundaryTitle": "Κανένα Διάσχισμα Ορίων", "boundaryMessage": "Αυτό το πλοίο δεν έχει διασχίσει κανένα όριο γεω-περίφραξης ακόμα.", "boundaryFiltered": "Δεν βρέθηκαν γεγονότα με τα επιλεγμένα φίλτρα."}, "filter": {"status": "Κατάσταση:", "all": "Όλα τα Γεγονότα", "allStatus": "Όλες οι Καταστάσεις", "allTemplates": "Όλα τα Πρότυπα", "systemEvents": "Γεγονότ<PERSON> Συστήματος", "analysisEvents": "Γεγονότα Ανάλυσης"}, "status": {"active": "Αυτή τη Στιγμή Έξω", "completed": "Επέστρεψε στην Περίφραξη", "outside": "Έξω", "ongoing": "Ακόμα έξω"}, "table": {"status": "Κατάσταση", "exitTime": "Ώρα Εξόδου", "exitCoords": "Συντεταγ<PERSON><PERSON><PERSON><PERSON>ς Εξόδου", "duration": "Διάρκεια", "entryTime": "Ώρα Επιστροφής"}, "activeEvents": {"title": "Πλοία Εκτός Περίφραξης", "empty": "Όλα τα πλοία βρίσκονται εντός της γεω-περίφραξης", "exitedAt": "Έξοδος", "ago": "πριν", "footer": "Ενημερώνεται αυτόματα κάθε 5 λεπτά", "error": {"title": "Σφάλμα Φόρτωσης Ενεργών Γεγονότων", "message": "Αποτυχ<PERSON><PERSON> φόρτωσης ενεργών γεγονότων περίφραξης."}}}}