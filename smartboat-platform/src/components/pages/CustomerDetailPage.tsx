import React, { useEffect, useState } from 'react';
import { useParams, useNavigate, Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { StatusBadge, LoadingOverlay, ErrorNotification } from '../common';
import { Customer } from '../../types';
import EditCustomerForm from '../features/customers/EditCustomerForm';
import AddCompanyForm from '../features/companies/AddCompanyForm';
import AddVesselForm from '../features/vessels/AddVesselForm';
import AddSensorForm from '../features/sensors/AddSensorForm';
import { useCustomer, useDeleteCustomer } from '../../hooks/queries/useCustomerQueries';
import { useCompanies } from '../../hooks/queries/useCompanyQueries';
import { useVessels } from '../../hooks/queries/useVesselQueries';
import { useSensors } from '../../hooks/queries/useSensorQueries';
import { useNotifications } from '../../hooks/queries/useNotificationQueries';
import { useSubscriptionsByCustomer } from '../../hooks/queries/useSubscriptionQueries';
import { formatDate } from '../../utils/dateUtils';
import { useAuth } from '../../context/AuthContext';

const CustomerDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const { currentUser } = useAuth();

  const [activeTab, setActiveTab] = useState('overview');
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [sidebarWidth, setSidebarWidth] = useState(30); // Width in percentage
  const [isDragging, setIsDragging] = useState(false); // Add state for dragging

  // Add form modal states
  const [isAddCompanyModalOpen, setIsAddCompanyModalOpen] = useState(false);
  const [isAddVesselModalOpen, setIsAddVesselModalOpen] = useState(false);
  const [isAddSensorModalOpen, setIsAddSensorModalOpen] = useState(false);
  const [selectedCompanyForVessel, setSelectedCompanyForVessel] = useState<any>(null);
  const [selectedVesselForSensor, setSelectedVesselForSensor] = useState<any>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const deleteCustomer = useDeleteCustomer();

  // Get customerId from URL parameter (keep as string for UUID)
  const customerId = id;

  // Helper function to get notification icon based on type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'error':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      case 'warning':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      case 'success':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
          </svg>
        );
    }
  };

  // Helper function to format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return t('notifications.justNow');
    if (diffMins < 60) return t('notifications.minutesAgo', { count: diffMins });
    if (diffHours < 24) return t('notifications.hoursAgo', { count: diffHours });
    if (diffDays < 7) return t('notifications.daysAgo', { count: diffDays });

    return date.toLocaleDateString();
  };

  // Fetch customer data using React Query
  const {
    data: customer,
    isLoading: customerLoading,
    isError: customerError,
    error,
    refetch
  } = useCustomer(customerId, {
    useMockFallback: false,
    staleTime: 0,
    cacheTime: 0,
    retry: false,
    refetchOnMount: 'always', // Always refetch when component mounts
    refetchOnWindowFocus: false // Don't refetch on window focus
  });

  // Fetch all companies and filter by customer
  const {
    data: companiesResponse,
    isLoading: companiesLoading
  } = useCompanies({
    useMockFallback: false,
    staleTime: 0,
    cacheTime: 0,
    retry: false,
    refetchOnMount: 'always',
    refetchOnWindowFocus: false
  });

  // Extract companies array from response
  const allCompanies = companiesResponse?.data || [];

  // Fetch all vessels and filter by customer companies
  const {
    data: allVessels = [],
    isLoading: vesselsLoading
  } = useVessels({
    useMockFallback: false,
    staleTime: 0,
    cacheTime: 0,
    retry: false,
    refetchOnMount: 'always',
    refetchOnWindowFocus: false
  });

  // Fetch all sensors and filter by customer vessels
  // Temporarily disable sensor API call as it may not exist
  const {
    data: allSensors = [],
    isLoading: sensorsLoading
  } = useSensors({
    useMockFallback: false,
    staleTime: 0,
    cacheTime: 0
  });

  // Fetch recent notifications for the current user
  const {
    data: customerNotifications = [],
    isLoading: notificationsLoading
  } = useNotifications({
    pageLimit: 5, // Show only recent 5 notifications
    useMockFallback: false
  });

  // Fetch subscriptions for this customer
  const {
    data: customerSubscriptions = [],
    isLoading: subscriptionsLoading,
    isError: subscriptionsError
  } = useSubscriptionsByCustomer(customerId, {
    useMockFallback: false,
    staleTime: 0,
    cacheTime: 0,
    retry: false,
    refetchOnMount: 'always',
    refetchOnWindowFocus: false
  });

  // Check if we should open edit modal based on location state
  useEffect(() => {
    if (location.state && location.state.editMode) {
      setIsEditModalOpen(true);
    }
  }, [location.state]);


  // Filter data based on customer
  const customerCompanies = Array.isArray(allCompanies) ? allCompanies.filter((company: any) =>
    company.customerId?.toString() === customerId
  ) : [];

  const customerVessels = Array.isArray(allVessels) ? allVessels.filter((vessel: any) => {
    const companyIds = customerCompanies.map((company: any) => company.id);
    // Check if vessel has a company object with an id that matches
    if (vessel.company && vessel.company.id) {
      return companyIds.indexOf(vessel.company.id) !== -1;
    }
    // Fallback to companyId field for mock data compatibility
    if (vessel.companyId) {
      return companyIds.indexOf(vessel.companyId) !== -1;
    }
    return false;
  }) : [];

  // Get sensors from both separate API call and nested within vessels
  const sensorsFromAPI = Array.isArray(allSensors) ? allSensors.filter((sensor: any) => {
    if (!sensor.vesselId) return false;

    const vesselIds = customerVessels.map((vessel: any) => vessel.id);
    // Convert both to strings for comparison to handle type mismatches
    const vesselIdStrings = vesselIds.map((id: any) => String(id));
    const sensorVesselIdString = String(sensor.vesselId);

    // Check for direct match first
    const directMatch = vesselIdStrings.includes(sensorVesselIdString);

    // If no direct match and we have mock data scenario (simple sensor vesselId vs UUID vessel IDs),
    // include all sensors for this customer (assume they belong to this customer)
    const isMockScenario = !directMatch &&
      vesselIds.some((id: any) => String(id).includes('-')) &&
      sensorVesselIdString.length <= 2;

    // Debug logging for sensor filtering
    console.log('DEBUG - Sensor filtering:', {
      sensorId: sensor.id,
      sensorName: sensor.name,
      sensorVesselId: sensor.vesselId,
      vesselIds: vesselIds,
      directMatch: directMatch,
      isMockScenario: isMockScenario,
      finalMatch: directMatch || isMockScenario
    });

    return directMatch || isMockScenario;
  }) : [];

  // Get sensors nested within vessels
  const sensorsFromVessels = customerVessels.reduce((acc: any[], vessel: any) => {
    // Handle both array (from detail API) and number (from list API) cases
    if (vessel.sensors && Array.isArray(vessel.sensors)) {
      const validSensors = vessel.sensors
        .filter((sensor: any) => sensor && typeof sensor === 'object' && sensor.id)
        .map((sensor: any) => {
          // Ensure we only copy safe properties
          return {
            id: sensor.id,
            name: sensor.name || 'Unknown Sensor',
            type: sensor.type || 'Unknown',
            status: sensor.status || 'Unknown',
            lastReading: sensor.lastReading,
            lastUpdated: sensor.lastUpdated,
            vesselId: vessel.id,
            vesselName: vessel.name
          };
        });
      return acc.concat(validSensors);
    }
    // If sensors is a number (from list API), we can't extract individual sensors
    // This will be handled by the separate sensors API call
    return acc;
  }, []);

  // Combine sensors from both sources
  let customerSensors = [];
  try {
    customerSensors = [...sensorsFromAPI, ...sensorsFromVessels];

    // Remove duplicates based on ID
    const uniqueSensors = customerSensors.reduce((acc: any[], sensor: any) => {
      if (!acc.find((s: any) => s.id === sensor.id)) {
        acc.push(sensor);
      }
      return acc;
    }, []);

    customerSensors = uniqueSensors;

    // Debug logging
    console.log('DEBUG - Customer sensors data:', {
      allSensors: allSensors?.length || 0,
      customerVessels: customerVessels?.length || 0,
      sensorsFromAPI: sensorsFromAPI?.length || 0,
      sensorsFromVessels: sensorsFromVessels?.length || 0,
      customerSensors: customerSensors?.length || 0,
      sampleVessel: customerVessels?.[0]?.sensors,
      vesselSensorTypes: customerVessels?.map(v => ({
        id: v.id,
        name: v.name,
        sensorsType: Array.isArray(v.sensors) ? 'array' : typeof v.sensors,
        sensorsCount: Array.isArray(v.sensors) ? v.sensors.length : v.sensors
      }))
    });
  } catch (error) {
    console.error('Error combining sensors:', error);
    customerSensors = [];
  }


  // Calculate real counts for overview
  const realCounts = {
    companies: customerCompanies.length,
    vessels: customerVessels.length,
    sensors: customerSensors.length > 0 ? customerSensors.length :
      // Fallback: calculate from vessel sensor counts if no individual sensors found
      customerVessels.reduce((sum, vessel) => sum + (Array.isArray(vessel.sensors) ? vessel.sensors.length : (vessel.sensors || 0)), 0)
  };

  // Navigation handlers
  const handleBack = () => {
    navigate('/customers');
  };

  const handleSelectCompany = (companyId: number) => {
    navigate(`/companies/${companyId}`);
  };

  const handleSelectVessel = (vesselId: number) => {
    navigate(`/vessels/${vesselId}`);
  };

  // Handle customer deletion
  const handleDeleteCustomer = async () => {
    try {
      await deleteCustomer.mutateAsync(customerId);
      setShowDeleteConfirm(false);
      navigate('/customers');
    } catch (error) {
      console.error('Failed to delete customer:', error);
      alert(t('customers.deleteFailed'));
    }
  };

  // Add handlers for form modals
  const handleAddCompany = () => {
    setIsAddCompanyModalOpen(true);
  };

  const handleAddVessel = (company?: any) => {
    if (company) {
      setSelectedCompanyForVessel(company);
    } else if (customerCompanies.length > 0) {
      setSelectedCompanyForVessel(customerCompanies[0]);
    } else {
      // No companies available, don't open the modal
      console.warn('Cannot add vessel: no companies available for customer');
      return;
    }
    setIsAddVesselModalOpen(true);
  };

  const handleAddSensor = (vessel?: any) => {
    if (vessel) {
      setSelectedVesselForSensor(vessel);
    } else if (customerVessels.length > 0) {
      setSelectedVesselForSensor(customerVessels[0]);
    } else {
      // No vessels available, don't open the modal
      console.warn('Cannot add sensor: no vessels available for customer');
      return;
    }
    setIsAddSensorModalOpen(true);
  };

  const handleCompanyCreated = (newCompany: any) => {
    setIsAddCompanyModalOpen(false);
    // Refetch data to update the UI
    refetch();
  };

  const handleVesselCreated = (newVessel: any) => {
    setIsAddVesselModalOpen(false);
    setSelectedCompanyForVessel(null);
    // Refetch data to update the UI
    refetch();
  };

  const handleSensorCreated = (newSensor: any) => {
    setIsAddSensorModalOpen(false);
    setSelectedVesselForSensor(null);
    // Refetch data to update the UI
    refetch();
  };

  // Combined loading state - wait for ALL calls to complete
  // Note: sensorsLoading excluded since sensor API call is disabled
  const isLoading = customerLoading || companiesLoading || vesselsLoading;

  // Loading state
  if (isLoading) {
    const waitingFor = [
      customerLoading && 'customer',
      companiesLoading && 'companies',
      vesselsLoading && 'vessels'
    ].filter(Boolean).join(', ');

    return (
      <div className="container mx-auto relative min-h-screen">
        <LoadingOverlay
          message="Loading customer data..."
          subMessage={`Waiting for: ${waitingFor}`}
        />
      </div>
    );
  }


  // Error state
  if (customerError) {
    return (
      <div className="container mx-auto">
        <ErrorNotification
          message={t('common.errorFetchingData')}
          error={error}
          onRetry={refetch}
        />
      </div>
    );
  }

  // Customer not found
  if (!customer) {
    return (
      <div className="bg-white rounded-lg shadow p-8 text-center">
        <h3 className="text-lg font-medium text-gray-900 mb-2">{t('customers.customerNotFound')}</h3>
        <Link
          to="/customers"
          className="mt-4 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md inline-block"
        >
          {t('customers.backToCustomers')}
        </Link>
      </div>
    );
  }


  // Handle customer update
  const handleSaveCustomer = (updatedCustomer: Customer) => {
    setIsEditModalOpen(false);
    // Refetch customer data to get the latest from the API
    refetch();
    console.log('Customer updated:', updatedCustomer);
  };

  // Improved resize handler with isDragging state
  function startResize(e: React.MouseEvent) {
    e.preventDefault();
    setIsDragging(true);

    // Define handlers inline to avoid hook issues
    const onMouseMove = (moveEvent: MouseEvent) => {
      // Calculate new width based on mouse position
      const windowWidth = window.innerWidth;
      const mouseX = moveEvent.clientX;

      // Convert to percentage (100 - x because we're dragging from right side)
      const widthPercent = 100 - (mouseX / windowWidth * 100);

      // Limit to reasonable values (between 20% and 80%)
      const newWidth = Math.max(20, Math.min(80, widthPercent));
      setSidebarWidth(newWidth);
    };

    const onMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };

    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
  }

  return (
    <div className="container mx-auto">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <Link
            to="/customers"
            className="flex items-center text-blue-600 hover:text-blue-800"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
            {t('customers.backToCustomers')}
          </Link>
          <h1 className="text-2xl font-semibold mt-2">{customer.name}</h1>
          <div className="flex items-center mt-1">
            <span className="text-gray-600 mr-2">{t('common.status')}:</span>
            <StatusBadge status={customer.status} />
            <span className="ml-3 text-sm text-gray-500">{t('common.lastActive')}: {customer.lastActive}</span>
          </div>
        </div>
        <div className="flex space-x-3">
          <button
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
            onClick={() => setIsEditModalOpen(true)}
          >
            {t('customers.editCustomer')}
          </button>
          <button
            className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md"
            onClick={() => setShowDeleteConfirm(true)}
          >
            {t('customers.deleteCustomer')}
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex">
            <button
              className={`py-4 px-6 ${
                activeTab === 'overview'
                  ? 'border-b-2 border-blue-500 text-blue-600 font-medium'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('overview')}
            >
              {t('customers.tabs.overview')}
            </button>
            <button
              className={`py-4 px-6 ${
                activeTab === 'companies'
                  ? 'border-b-2 border-blue-500 text-blue-600 font-medium'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('companies')}
            >
              {t('customers.tabs.companies')}
            </button>
            <button
              className={`py-4 px-6 ${
                activeTab === 'vessels'
                  ? 'border-b-2 border-blue-500 text-blue-600 font-medium'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('vessels')}
            >
              {t('customers.tabs.vessels')}
            </button>
            <button
              className={`py-4 px-6 ${
                activeTab === 'sensors'
                  ? 'border-b-2 border-blue-500 text-blue-600 font-medium'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('sensors')}
            >
              {t('common.sensors')}
            </button>
            <button
              className={`py-4 px-6 ${
                activeTab === 'billing'
                  ? 'border-b-2 border-blue-500 text-blue-600 font-medium'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('billing')}
            >
              {t('customers.tabs.subscriptionBilling')}
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Customer Details */}
              <div className="md:col-span-1">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-4">{t('customers.customerDetails')}</h4>

                  <div className="space-y-4 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('customers.customerName')}:</span>
                      <span className="text-gray-900 font-medium">{customer.name}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('common.status')}:</span>
                      <span className="text-gray-900 font-medium">
                        <StatusBadge status={customer.status} />
                      </span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('common.vessels')}:</span>
                      <span className="text-gray-900 font-medium">{realCounts.vessels}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('common.sensors')}:</span>
                      <span className="text-gray-900 font-medium">{realCounts.sensors}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('common.lastActive')}:</span>
                      <span className="text-gray-900 font-medium">
                        {customer.lastActive ? new Date(customer.lastActive).toLocaleString('el-GR', {
                          day: '2-digit',
                          month: '2-digit',
                          year: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        }) : '-'}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="mt-4 bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-4">{t('customers.contactDetails')}</h4>

                  <div className="space-y-4 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('common.contactPerson')}:</span>
                      <span className="text-gray-900 font-medium">{customer.contactPerson}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('common.email')}:</span>
                      <span className="text-gray-900 font-medium">{customer.email}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('common.phone')}:</span>
                      <span className="text-gray-900 font-medium">{customer.phone}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Subscription Overview */}
              <div className="md:col-span-2">
                <h4 className="font-medium text-gray-900 mb-4">{t('customers.subscriptionOverview')}</h4>

                {subscriptionsLoading ? (
                  <div className="bg-gray-50 p-4 rounded-lg mb-4 animate-pulse">
                    <div className="flex justify-between items-center mb-2">
                      <div className="h-5 bg-gray-200 rounded w-48"></div>
                      <div className="h-4 bg-gray-200 rounded w-20"></div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="h-4 bg-gray-200 rounded w-32 mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-40"></div>
                      </div>
                      <div className="text-right">
                        <div className="h-5 bg-gray-200 rounded w-24 mb-1"></div>
                        <div className="h-4 bg-gray-200 rounded w-20"></div>
                      </div>
                    </div>
                  </div>
                ) : subscriptionsError ? (
                  <div className="bg-red-50 border border-red-200 p-4 rounded-lg mb-4">
                    <p className="text-red-600 text-sm">{t('customers.subscription.errorLoading')}</p>
                  </div>
                ) : customerSubscriptions.length > 0 ? (
                  customerSubscriptions.map((subscription: any) => (
                    <div key={subscription.id} className="bg-gray-50 p-4 rounded-lg mb-4">
                      <div className="mb-2">
                        <h5 className="font-medium">
                          {t('customers.subscription.currentPlan')}:
                          <span className="text-blue-600 ml-1">{subscription.type || subscription.name}</span>
                        </h5>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-gray-500">
                            {t('subscriptions.billingFrequency')}: {subscription.billingFrequency || 'Monthly'}
                          </p>
                          <p className="text-sm text-gray-500">
                            {t('subscriptions.endDate')}: {subscription.endDate ?
                              new Date(subscription.endDate).toLocaleDateString('el-GR', {
                                day: '2-digit',
                                month: '2-digit',
                                year: 'numeric'
                              }) : 'N/A'
                            }
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-gray-900 font-semibold">
                            ${subscription.price || 0} / {subscription.billingFrequency?.toLowerCase() || 'month'}
                          </p>
                          {subscription.sensorLimit && (
                            <p className="text-sm text-gray-500">
                              {subscription.sensorLimit} {t('customers.subscription.sensorLimit')}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="bg-gray-50 p-4 rounded-lg mb-4">
                    <p className="text-gray-500 text-center">{t('customers.subscription.noSubscriptions')}</p>
                  </div>
                )}

                <h4 className="font-medium text-gray-900 mb-2">{t('common.statistics')}</h4>

                <div className="grid grid-cols-3 gap-4 mb-4">
                  {(() => {
                    // Calculate subscription limits from active subscriptions
                    const activeSub = customerSubscriptions.find((sub: any) => sub.status === 'Active') || customerSubscriptions[0];
                    const sensorLimit = activeSub?.sensorLimit;

                    // Use reasonable defaults based on subscription type if no limits are set
                    const getDefaultLimits = (type: string) => {
                      switch(type) {
                        case 'Standard': return { companies: 2, vessels: 5, sensors: 10 };
                        case 'Professional': return { companies: 5, vessels: 15, sensors: 25 };
                        case 'Enterprise': return { companies: null, vessels: null, sensors: null };
                        default: return { companies: 5, vessels: 15, sensors: sensorLimit || 50 };
                      }
                    };

                    const limits = activeSub ? getDefaultLimits(activeSub.type) : { companies: 5, vessels: 15, sensors: 50 };

                    return (
                      <>
                        <div className="bg-blue-50 p-4 rounded-lg text-center">
                          <h5 className="text-xs text-gray-500 uppercase">{t('common.companies')}</h5>
                          <p className="text-2xl font-bold text-blue-600">{realCounts.companies}</p>
                          <div className="mt-1 w-full bg-gray-200 rounded-full h-2.5">
                            <div className="bg-blue-600 h-2.5 rounded-full" style={{
                              width: limits.companies ? `${Math.min((realCounts.companies / limits.companies) * 100, 100)}%` : '100%'
                            }}></div>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">
                            {limits.companies ? `${limits.companies} ${t('customers.subscription.allowed')}` : t('customers.subscription.unlimited')}
                          </p>
                        </div>

                        <div className="bg-blue-50 p-4 rounded-lg text-center">
                          <h5 className="text-xs text-gray-500 uppercase">{t('common.vessels')}</h5>
                          <p className="text-2xl font-bold text-blue-600">{realCounts.vessels}</p>
                          <div className="mt-1 w-full bg-gray-200 rounded-full h-2.5">
                            <div className="bg-blue-600 h-2.5 rounded-full" style={{
                              width: limits.vessels ? `${Math.min((realCounts.vessels / limits.vessels) * 100, 100)}%` : '100%'
                            }}></div>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">
                            {limits.vessels ? `${limits.vessels} ${t('customers.subscription.allowed')}` : t('customers.subscription.unlimited')}
                          </p>
                        </div>

                        <div className="bg-blue-50 p-4 rounded-lg text-center">
                          <h5 className="text-xs text-gray-500 uppercase">{t('common.sensors')}</h5>
                          <p className="text-2xl font-bold text-blue-600">{realCounts.sensors}</p>
                          <div className="mt-1 w-full bg-gray-200 rounded-full h-2.5">
                            <div className="bg-blue-600 h-2.5 rounded-full" style={{
                              width: limits.sensors ? `${Math.min((realCounts.sensors / limits.sensors) * 100, 100)}%` : '100%'
                            }}></div>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">
                            {limits.sensors ? `${limits.sensors} ${t('customers.subscription.allowed')}` : t('customers.subscription.unlimited')}
                          </p>
                        </div>
                      </>
                    );
                  })()}
                </div>

                <h4 className="font-medium text-gray-900 dark:text-white mb-2">{t('customers.recentActivity')}</h4>

                {notificationsLoading ? (
                  <div className="space-y-3">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg animate-pulse">
                        <div className="flex items-start space-x-3">
                          <div className="w-5 h-5 bg-gray-200 dark:bg-gray-600 rounded-full"></div>
                          <div className="flex-1">
                            <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-3/4 mb-2"></div>
                            <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-1/4"></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : customerNotifications.length > 0 ? (
                  <div className="space-y-3">
                    {customerNotifications.map((notification: any) => (
                      <div key={notification.id} className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg flex items-start">
                        <div className="flex-shrink-0 mt-1">
                          {getNotificationIcon(notification.type)}
                        </div>
                        <div className="ml-3 flex-1">
                          <div className="flex justify-between">
                            <p className="text-sm text-gray-700 dark:text-gray-300">
                              {notification.title && (
                                <span className="font-medium">{notification.title}</span>
                              )}
                              {notification.message && notification.title && ' - '}
                              {notification.message}
                            </p>
                            <span className="text-xs text-gray-500 dark:text-gray-400 flex-shrink-0 ml-2">
                              {formatTimestamp(notification.timestamp)}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg text-center">
                    <p className="text-sm text-gray-500 dark:text-gray-400">{t('notifications.noNotifications')}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'companies' && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h4 className="font-medium text-gray-900">{t('common.companies')}</h4>
                <button
                  onClick={handleAddCompany}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 text-sm rounded-md flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                  </svg>
                  {t('customers.companies.addCompany')}
                </button>
              </div>

              {customerCompanies.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('common.name')}
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('common.location')}
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Industry
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('common.status')}
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('common.lastUpdated')}
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('common.actions')}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {customerCompanies.map(company => (
                        <tr key={company.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-bold">
                                {company.name.charAt(0)}
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">{company.name}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {company.location}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {company.industry}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <StatusBadge status={company.status} />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {company.lastUpdated ? new Date(company.lastUpdated).toLocaleString('el-GR', {
                              day: '2-digit',
                              month: '2-digit',
                              year: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            }) : '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button
                              className="text-blue-600 hover:text-blue-900 mr-3"
                              onClick={() => handleSelectCompany(company.id)}
                            >
                              {t('common.view')}
                            </button>
                            <button
                              className="text-blue-600 hover:text-blue-900"
                              onClick={() => navigate(`/companies/${company.id}`, { state: { editMode: true } })}
                            >
                              {t('common.edit')}
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-12">
                  <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">{t('customers.companies.noCompanies')}</h3>
                  <p className="mt-1 text-sm text-gray-500">{t('customers.companies.noCompaniesMessage')}</p>
                  <div className="mt-6">
                    <button
                      type="button"
                      onClick={handleAddCompany}
                      className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <svg className="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                      </svg>
                      {t('customers.companies.addCompany')}
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'vessels' && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h4 className="font-medium text-gray-900">{t('customers.vessels.allVessels')}</h4>
                <button
                  onClick={() => handleAddVessel()}
                  disabled={customerCompanies.length === 0}
                  className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-3 py-1 text-sm rounded-md flex items-center"
                  title={customerCompanies.length === 0 ? t('customers.vessels.needCompanyFirst') : ''}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                  </svg>
                  {t('customers.vessels.addVesselToCompany')}
                </button>
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('common.name')}
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('common.type')}
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('common.status')}
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('common.company')}
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('common.sensors')}
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('common.location')}
                      </th>
                      <th scope="col" className="relative px-6 py-3">
                        <span className="sr-only">{t('common.actions')}</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {customerVessels.map(vessel => (
                      <tr key={vessel.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="font-medium text-gray-900">{vessel.name}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {vessel.type}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <StatusBadge status={vessel.status} />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {(() => {
                            const companyId = vessel.company?.id || vessel.companyId;
                            const company = customerCompanies.filter((c: any) => c.id === companyId)[0];
                            return company?.name || t('customers.vessels.unknownCompany');
                          })()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {Array.isArray(vessel.sensors) ? vessel.sensors.length : (vessel.sensors || 0)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {typeof vessel.location === 'string'
                            ? vessel.location
                            : vessel.location
                              ? `${vessel.location.latitude.toFixed(4)}, ${vessel.location.longitude.toFixed(4)}`
                              : vessel.homePort || t('vessels.locationUnknown')
                          }
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            className="text-blue-600 hover:text-blue-900"
                            onClick={() => handleSelectVessel(vessel.id)}
                          >
                            {t('common.view')}
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {customerVessels.length === 0 && (
                <div className="text-center py-12">
                  <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">{t('customers.vessels.noVesselsFound')}</h3>
                  <p className="mt-1 text-sm text-gray-500">{t('customers.vessels.noVesselsMessage')}</p>
                  <div className="mt-6">
                    {customerCompanies.length > 0 ? (
                      <button
                        type="button"
                        onClick={() => handleAddVessel()}
                        className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <svg className="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                        </svg>
                        {t('customers.vessels.addVesselToCompany')}
                      </button>
                    ) : (
                      <div className="text-center">
                        <button
                          type="button"
                          onClick={handleAddCompany}
                          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                        >
                          <svg className="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                          </svg>
                          {t('customers.companies.addCompanyFirst')}
                        </button>
                        <p className="mt-2 text-xs text-gray-500">{t('customers.vessels.needCompanyMessage')}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'sensors' && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h4 className="font-medium text-gray-900">{t('common.sensors')}</h4>
                <button
                  onClick={() => handleAddSensor()}
                  disabled={customerVessels.length === 0}
                  className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-3 py-1 text-sm rounded-md flex items-center"
                  title={customerVessels.length === 0 ? t('sensors.needVesselFirst') : ''}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                  </svg>
                  {t('sensors.addSensor')}
                </button>
              </div>


              {customerSensors.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('common.name')}
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('sensors.type')}
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('common.vessel')}
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('common.status')}
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('common.actions')}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {customerSensors.map((sensor, index) => {
                        // Safety check for sensor object
                        if (!sensor || typeof sensor !== 'object' || !sensor.id) {
                          console.warn('Invalid sensor object:', sensor);
                          return null;
                        }

                        return (
                        <tr key={sensor.id || index} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10 rounded-full bg-green-100 flex items-center justify-center text-green-600 font-bold">
                                {sensor.name ? sensor.name.charAt(0).toUpperCase() : '?'}
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">{sensor.name || 'Unknown Sensor'}</div>
                                <div className="text-sm text-gray-500">ID: {sensor.id}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {sensor.type || 'Unknown'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {(() => {
                              const vessel = customerVessels.filter((v: any) => v.id === sensor.vesselId)[0];
                              return vessel?.name || sensor.vesselName || t('sensors.unknownVessel');
                            })()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <StatusBadge status={sensor.status} />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button
                              className="text-blue-600 hover:text-blue-900 mr-3"
                              onClick={() => navigate(`/sensors/${sensor.id}`)}
                            >
                              {t('common.view')}
                            </button>
                            <button
                              className="text-blue-600 hover:text-blue-900"
                              onClick={() => navigate(`/sensors/${sensor.id}`, { state: { editMode: true } })}
                            >
                              {t('common.edit')}
                            </button>
                          </td>
                        </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-12">
                  <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">{t('sensors.noSensorsFound')}</h3>
                  <p className="mt-1 text-sm text-gray-500">{t('sensors.noSensorsMessage')}</p>
                  <div className="mt-6">
                    {customerVessels.length > 0 ? (
                      <button
                        type="button"
                        onClick={() => handleAddSensor()}
                        className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <svg className="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                        </svg>
                        {t('sensors.addSensor')}
                      </button>
                    ) : customerCompanies.length > 0 ? (
                      <div className="text-center">
                        <button
                          type="button"
                          onClick={() => handleAddVessel()}
                          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                        >
                          <svg className="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                          </svg>
                          {t('customers.vessels.addVesselFirst')}
                        </button>
                        <p className="mt-2 text-xs text-gray-500">{t('sensors.needVesselMessage')}</p>
                      </div>
                    ) : (
                      <div className="text-center">
                        <button
                          type="button"
                          onClick={handleAddCompany}
                          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                        >
                          <svg className="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                          </svg>
                          {t('customers.companies.addCompanyFirst')}
                        </button>
                        <p className="mt-2 text-xs text-gray-500">{t('sensors.needCompanyAndVesselMessage')}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'billing' && (
            <div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="md:col-span-1">
                  {subscriptionsLoading ? (
                    <div className="bg-gray-50 p-4 rounded-lg animate-pulse">
                      <div className="h-6 bg-gray-200 rounded w-1/2 mb-4"></div>
                      <div className="h-8 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-4 bg-gray-200 rounded w-full mb-4"></div>
                      <div className="space-y-3">
                        <div className="h-4 bg-gray-200 rounded w-full"></div>
                        <div className="h-4 bg-gray-200 rounded w-full"></div>
                        <div className="h-4 bg-gray-200 rounded w-full"></div>
                      </div>
                    </div>
                  ) : subscriptionsError ? (
                    <div className="bg-red-50 border border-red-200 p-4 rounded-lg">
                      <p className="text-red-600 text-sm">{t('customers.subscription.errorLoading')}</p>
                    </div>
                  ) : customerSubscriptions.length > 0 ? (
                    customerSubscriptions.map((subscription: any) => (
                      <div key={subscription.id} className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-medium text-gray-900 mb-4">{t('customers.subscription.currentPlan')}</h4>

                        <div className="mb-4">
                          <h5 className="font-semibold text-blue-600 text-xl">{subscription.type || subscription.name}</h5>
                          {subscription.sensorLimit && (
                            <p className="text-gray-600 mt-1">
                              {subscription.sensorLimit === null ? t('customers.subscription.unlimited') : `${subscription.sensorLimit} ${t('customers.subscription.sensorLimit')}`}
                            </p>
                          )}
                        </div>

                        <div className="text-sm space-y-3">
                          <div className="flex justify-between">
                            <span className="text-gray-500">{t('subscriptions.price')}:</span>
                            <span className="text-gray-900 font-medium">
                              {subscription.price ? `$${subscription.price}` : 'N/A'}
                            </span>
                          </div>

                          <div className="flex justify-between">
                            <span className="text-gray-500">{t('subscriptions.billingFrequency')}:</span>
                            <span className="text-gray-900 font-medium">
                              {subscription.billingFrequency || 'N/A'}
                            </span>
                          </div>

                          <div className="flex justify-between">
                            <span className="text-gray-500">{t('subscriptions.endDate')}:</span>
                            <span className="text-gray-900 font-medium">
                              {formatDate(subscription.endDate)}
                            </span>
                          </div>

                          <div className="flex justify-between">
                            <span className="text-gray-500">{t('common.status')}:</span>
                            <span className={`text-sm font-medium ${
                              subscription.status === 'Active' ? 'text-green-600' :
                              subscription.status === 'Pending' ? 'text-yellow-600' :
                              subscription.status ? 'text-red-600' : 'text-gray-500'
                            }`}>
                              {subscription.status || 'N/A'}
                            </span>
                          </div>
                        </div>

                        {/* Hide action buttons since payment/billing management is not available via API */}
                        {/* Only show if user has admin permissions */}
                        {currentUser?.role === 'Administrator' && (
                          <div className="mt-6 space-y-3">
                            <button
                              onClick={() => navigate(`/subscriptions/${subscription.id}`)}
                              className="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
                            >
                              {t('subscriptions.viewDetails')}
                            </button>
                          </div>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <p className="text-gray-500 text-center">{t('customers.subscription.noSubscriptions')}</p>
                    </div>
                  )}
                </div>

                <div className="md:col-span-2">
                  {/* Hide billing history since it's not available from the API */}
                  {customerSubscriptions.length > 0 ? (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-4">{t('customers.subscription.subscriptionDetails')}</h4>

                      {customerSubscriptions.map((subscription: any) => (
                        <div key={subscription.id} className="bg-white border border-gray-200 rounded-lg p-6 mb-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <h5 className="font-medium text-gray-900 mb-3">{t('customers.subscription.subscriptionDetails')}</h5>
                              <div className="space-y-2 text-sm">
                                <div className="flex justify-between">
                                  <span className="text-gray-500">{t('common.name')}:</span>
                                  <span className="text-gray-900">{subscription.name}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-gray-500">{t('common.type')}:</span>
                                  <span className="text-gray-900">{subscription.type}</span>
                                </div>
                                {subscription.startDate && (
                                  <div className="flex justify-between">
                                    <span className="text-gray-500">{t('subscriptions.startDate')}:</span>
                                    <span className="text-gray-900">{formatDate(subscription.startDate)}</span>
                                  </div>
                                )}
                                {subscription.endDate && (
                                  <div className="flex justify-between">
                                    <span className="text-gray-500">{t('subscriptions.endDate')}:</span>
                                    <span className="text-gray-900">{formatDate(subscription.endDate)}</span>
                                  </div>
                                )}
                              </div>
                            </div>

                            {subscription.features && subscription.features.length > 0 && (
                              <div>
                                <h5 className="font-medium text-gray-900 mb-3">{t('subscriptions.features')}</h5>
                                <ul className="text-sm text-gray-600 space-y-1">
                                  {subscription.features.map((feature: string, index: number) => (
                                    <li key={index} className="flex items-start">
                                      <span className="text-green-500 mr-2">•</span>
                                      {feature}
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="bg-gray-50 p-8 rounded-lg text-center">
                      <p className="text-gray-500">{t('customers.subscription.noSubscriptionDetails')}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Edit Customer Slideover */}
      <div
        className={`fixed inset-y-0 right-0 bg-white shadow-xl transform transition-all duration-300 ease-in-out z-50 ${
          isEditModalOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
        style={{ width: `${sidebarWidth}%` }}
      >
        {/* Resize handle */}
        <div
          className="absolute top-0 bottom-0 -left-1 w-2 cursor-col-resize"
          onMouseDown={startResize}
        >
          <div className="absolute top-1/2 transform -translate-y-1/2 -left-3 w-6 h-12 bg-blue-500 hover:bg-blue-600 rounded-l-md flex items-center justify-center cursor-col-resize group transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
            </svg>
            <span className="absolute left-0 ml-8 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              {t('common.dragToResize')}
            </span>
          </div>
        </div>

        {/* Indicator for when dragging is active */}
        {isDragging && (
          <div className="fixed inset-0 cursor-col-resize z-40" />
        )}

        <div className="h-full">
          {isEditModalOpen && (
            <EditCustomerForm
              customer={customer}
              onSave={handleSaveCustomer}
              onCancel={() => setIsEditModalOpen(false)}
            />
          )}
        </div>
      </div>

      {/* Backdrop */}
      {isEditModalOpen && (
        <div
          className="fixed inset-0 bg-gray-600 bg-opacity-50 z-40"
          onClick={() => setIsEditModalOpen(false)}
        ></div>
      )}

      {/* Add entity modals */}
      <AddCompanyForm
        customerId={customerId!}
        isOpen={isAddCompanyModalOpen}
        onSuccess={handleCompanyCreated}
        onCancel={() => setIsAddCompanyModalOpen(false)}
      />

      <AddVesselForm
        companyId={selectedCompanyForVessel?.id}
        companyName={selectedCompanyForVessel?.name}
        availableCompanies={customerCompanies.map(c => ({ id: c.id, name: c.name }))}
        isOpen={isAddVesselModalOpen}
        onSuccess={handleVesselCreated}
        onCancel={() => {
          setIsAddVesselModalOpen(false);
          setSelectedCompanyForVessel(null);
        }}
      />

      <AddSensorForm
        vesselId={selectedVesselForSensor?.id}
        vesselName={selectedVesselForSensor?.name}
        availableVessels={customerVessels.map(v => ({ id: v.id, name: v.name }))}
        isOpen={isAddSensorModalOpen}
        onSuccess={handleSensorCreated}
        onCancel={() => {
          setIsAddSensorModalOpen(false);
          setSelectedVesselForSensor(null);
        }}
      />

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <>
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 z-50" onClick={() => setShowDeleteConfirm(false)}></div>
          <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 z-50 max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              {t('customers.confirmDelete')}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
              {t('customers.deleteWarning', { customerName: customer?.name })}
            </p>
            <div className="flex justify-end space-x-3">
              <button
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md"
                onClick={() => setShowDeleteConfirm(false)}
                disabled={deleteCustomer.isPending}
              >
                {t('common.cancel')}
              </button>
              <button
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md disabled:opacity-50"
                onClick={handleDeleteCustomer}
                disabled={deleteCustomer.isPending}
              >
                {deleteCustomer.isPending ? t('common.deleting') : t('common.delete')}
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default CustomerDetailPage;
