import React from 'react';
import { useTranslation } from 'react-i18next';
import { useActiveFenceEvents } from '../../../hooks/queries/useGeoFencingQueries';
import geoFencingService from '../../../services/geoFencingService';

interface ActiveFenceEventsProps {
  className?: string;
}

interface FenceEvent {
  id: string;
  vesselId: string;
  vesselName?: string;
  exitTimestamp: string;
  exitLat: number;
  exitLng: number;
  status: 'Active';
  created: string;
}

const ActiveFenceEvents: React.FC<ActiveFenceEventsProps> = ({ 
  className = '' 
}) => {
  const { t } = useTranslation();

  const { 
    data: activeEvents = [], 
    isLoading, 
    isError, 
    error 
  } = useActiveFenceEvents();

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const formatCoordinates = (lat: number, lng: number) => {
    return `${lat.toFixed(4)}, ${lng.toFixed(4)}`;
  };

  const getTimeSinceExit = (exitTimestamp: string) => {
    const exitTime = new Date(exitTimestamp);
    const now = new Date();
    const diffMinutes = Math.floor((now.getTime() - exitTime.getTime()) / (1000 * 60));
    return geoFencingService.formatDuration(diffMinutes);
  };

  if (isError) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 dark:bg-red-900/20 dark:border-red-800 ${className}`}>
        <h3 className="text-sm font-medium text-red-800 dark:text-red-200 mb-1">
          {t('geoFencing.activeEvents.error.title', 'Error Loading Active Events')}
        </h3>
        <p className="text-xs text-red-700 dark:text-red-300">
          {t('geoFencing.activeEvents.error.message', 'Failed to load active fence events.')}
        </p>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Header */}
      <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
            <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {t('geoFencing.activeEvents.title', 'Vessels Outside Fence')}
            </h3>
            {!isLoading && (
              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300">
                {activeEvents.length}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {isLoading ? (
          <div className="flex items-center justify-center py-6">
            <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
            <span className="ml-2 text-xs text-gray-600 dark:text-gray-400">
              {t('common.loading', 'Loading...')}
            </span>
          </div>
        ) : activeEvents.length === 0 ? (
          <div className="text-center py-6">
            <div className="w-12 h-12 mx-auto mb-2 text-green-400">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {t('geoFencing.activeEvents.empty', 'All vessels are within the geo-fence')}
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {activeEvents.map((event: FenceEvent) => (
              <div 
                key={event.id}
                className="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/10 rounded-lg border border-red-200 dark:border-red-800"
              >
                <div className="flex-1 min-w-0">
                  <div className="flex items-center">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                      {event.vesselName || `Vessel ${event.vesselId.slice(0, 8)}`}
                    </p>
                    <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300">
                      {t('geoFencing.status.outside', 'Outside')}
                    </span>
                  </div>
                  <div className="flex items-center mt-1 text-xs text-gray-500 dark:text-gray-400">
                    <span>{t('geoFencing.activeEvents.exitedAt', 'Exited')}: {formatDateTime(event.exitTimestamp)}</span>
                    <span className="mx-1">•</span>
                    <span className="font-mono">{formatCoordinates(event.exitLat, event.exitLng)}</span>
                  </div>
                </div>
                <div className="ml-4 flex-shrink-0">
                  <div className="text-xs text-right">
                    <div className="font-medium text-red-600 dark:text-red-400">
                      {getTimeSinceExit(event.exitTimestamp)}
                    </div>
                    <div className="text-gray-500 dark:text-gray-400">
                      {t('geoFencing.activeEvents.ago', 'ago')}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      {activeEvents.length > 0 && (
        <div className="px-4 py-2 bg-gray-50 dark:bg-gray-900 rounded-b-lg border-t border-gray-200 dark:border-gray-700">
          <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
            {t('geoFencing.activeEvents.footer', 'Updates automatically every 5 minutes')}
          </p>
        </div>
      )}
    </div>
  );
};

export default ActiveFenceEvents;