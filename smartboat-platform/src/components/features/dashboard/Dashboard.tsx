import React, { useState, useEffect } from 'react';
import StatCard from './StatCard';
import VesselPanel from './VesselPanel';
import SensorPanel from './SensorPanel';
import RecentActivity from './RecentActivity';
import { ActiveFenceEvents } from '../vessels';
import { useAuth } from '../../../context/AuthContext';
import { useVessels, useSensors, useCompanies, useCustomers } from '../../../hooks';
import { useTranslation } from 'react-i18next';

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const { currentUser } = useAuth();
  const [stats, setStats] = useState({
    customers: 0,
    companies: 0,
    vessels: 0,
    activeSensors: 0
  });

  // Fetch all data from API instead of using mock data
  const { data: vesselsResponse } = useVessels();
  const { data: sensorsResponse } = useSensors();
  const { data: companiesResponse } = useCompanies();
  const { data: customersResponse } = useCustomers();

  // Extract data arrays from responses
  const vessels = vesselsResponse?.data || [];
  const sensors = sensorsResponse?.data || [];
  const companies = companiesResponse?.data || [];
  const customers = customersResponse?.data || [];

  useEffect(() => {
    // Filter data based on user role
    if (currentUser && Array.isArray(vessels) && Array.isArray(sensors) && Array.isArray(companies) && Array.isArray(customers)) {
      if (currentUser.role === 'Administrator' || currentUser.role === 'Manager') {
        // Admins and managers see all data
        setStats({
          customers: customers.length,
          companies: companies.length,
          vessels: vessels.length,
          activeSensors: sensors.filter(s => s.status === 'Active').length
        });
      } else if (currentUser.role === 'Customer' && currentUser.customerId) {
        // Filter companies by customer ID
        const customerCompanies = companies.filter(
          company => company.customerId === currentUser.customerId
        );

        // Get vessel IDs that belong to these companies
        const companyIds = customerCompanies.map(c => c.id);
        const customerVessels = vessels.filter(
          vessel => companyIds.includes(vessel.companyId)
        );

        // Get vessel IDs to filter sensors
        const vesselIds = customerVessels.map(v => v.id.toString());
        const customerSensors = sensors.filter(
          sensor => sensor.vesselId && vesselIds.includes(sensor.vesselId)
        );

        setStats({
          customers: 1, // Customer only sees themselves
          companies: customerCompanies.length,
          vessels: customerVessels.length,
          activeSensors: customerSensors.filter(s => s.status === 'Active').length
        });
      }
    }
  }, [currentUser, vessels, sensors, companies, customers]);

  return (
    <div className="container mx-auto">
      <h1 className="text-2xl font-semibold mb-6 text-gray-800 dark:text-white">{t('common.dashboard')}</h1>

      {/* Stats cards */}
      <div className="grid grid-cols-2 gap-6 mb-6">
        <StatCard
          title={t('dashboard.statCards.customers')}
          value={stats.customers}
          iconBgColor="bg-blue-100"
          iconTextColor="text-blue-500"
          icon={
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          }
        />

        <StatCard
          title={t('dashboard.statCards.companies')}
          value={stats.companies}
          iconBgColor="bg-green-100"
          iconTextColor="text-green-500"
          icon={
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0h5m5 0h2m-2-5h-5m-9 0h9" />
            </svg>
          }
        />

        <StatCard
          title={t('dashboard.statCards.vessels')}
          value={stats.vessels}
          iconBgColor="bg-yellow-100"
          iconTextColor="text-yellow-500"
          icon={
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          }
        />

        <StatCard
          title={t('dashboard.statCards.activeSensors')}
          value={stats.activeSensors}
          iconBgColor="bg-purple-100"
          iconTextColor="text-purple-500"
          icon={
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
            </svg>
          }
        />
      </div>

      {/* Main dashboard content */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Vessel Panel */}
        <VesselPanel />

        {/* Sensor Panel */}
        <SensorPanel />
      </div>

      {/* Recent Activity Section */}
      <RecentActivity />

      {/* Active Fence Events Section */}
      <div className="mt-6">
        <ActiveFenceEvents />
      </div>
    </div>
  );
};

export default Dashboard;