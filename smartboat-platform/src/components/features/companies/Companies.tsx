import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Company } from '../../../types';
import CompanyRow from './CompanyRow';
import EditCompanyForm from './EditCompanyForm';
import { useAuth } from '../../../context/AuthContext';
import { useCompanies, useCreateCompany } from '../../../hooks/queries/useCompanyQueries';
import { LoadingOverlay, ErrorNotification, Pagination } from '../../common';

const Companies: React.FC = () => {
  const { currentUser } = useAuth();
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterIndustry, setFilterIndustry] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [sidebarWidth, setSidebarWidth] = useState(30); // Width in percentage
  const [isDragging, setIsDragging] = useState(false); // Add state for dragging

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);

  // Calculate page offset (backend uses 0-based offset)
  const pageOffset = (currentPage - 1) * itemsPerPage;

  // Query parameters with pagination
  const queryOptions = {
    // Pagination parameters
    pageLimit: itemsPerPage,
    pageOffset: pageOffset,
    sortField: 'name',
    sortOrder: 'ASC',
    // Search and filter parameters
    ...(searchQuery && { searchTerm: searchQuery }),
    ...(filterStatus !== 'all' && { status: filterStatus }),
    // Note: Industry filtering is not supported by backend yet, so we'll do client-side filtering for now
    // Fallback to mock data if API fails
    useMockFallback: true
  };

  // Fetch companies using React Query
  const { data: companiesResponse, isLoading, error, refetch } = useCompanies(queryOptions);

  // Extract data and metadata from response
  const allCompanies = companiesResponse?.data || [];
  const metadata = companiesResponse?.metadata || { pageLimit: itemsPerPage, pageOffset: pageOffset, total: 0 };

  // Create mutation hook
  const createCompany = useCreateCompany();

  // Filter companies based on user role
  const roleFilteredCompanies = currentUser ?
    (currentUser.role === 'Administrator' || currentUser.role === 'Manager') ?
      allCompanies :
      currentUser.role === 'Customer' ?
        // For Customer role, if customerId is not set, show all companies for now
        // In production, this should be properly configured
        currentUser.customerId ?
          allCompanies.filter(company => company.customerId === currentUser.customerId) :
          allCompanies :
        [] :
    allCompanies;

  // Apply client-side industry filtering (since backend doesn't support it yet)
  const companies = filterIndustry === 'all'
    ? roleFilteredCompanies
    : roleFilteredCompanies.filter(company =>
        company.industry.toLowerCase() === filterIndustry.toLowerCase()
      );

  // Calculate pagination values
  const totalPages = Math.ceil((metadata.total || 0) / itemsPerPage);

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Handle search and filter changes (reset to first page)
  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    setCurrentPage(1);
  };

  const handleFilterStatusChange = (value: string) => {
    setFilterStatus(value);
    setCurrentPage(1);
  };

  const handleFilterIndustryChange = (value: string) => {
    setFilterIndustry(value);
    setCurrentPage(1);
  };

  // Handle adding a new company
  const handleAddCompany = (newCompany: Company) => {
    createCompany.mutate(newCompany, {
      onSuccess: () => {
        setIsCreateModalOpen(false);
      },
      onError: (error) => {
        console.error('Failed to create company:', error);
        // You could add an error notification here
      }
    });
  };

  // Improved resize handler with isDragging state
  function startResize(e: React.MouseEvent) {
    e.preventDefault();
    setIsDragging(true);

    // Define handlers inline to avoid hook issues
    const onMouseMove = (moveEvent: MouseEvent) => {
      // Calculate new width based on mouse position
      const windowWidth = window.innerWidth;
      const mouseX = moveEvent.clientX;

      // Convert to percentage (100 - x because we're dragging from right side)
      const widthPercent = 100 - (mouseX / windowWidth * 100);

      // Limit to reasonable values (between 20% and 80%)
      const newWidth = Math.max(20, Math.min(80, widthPercent));
      setSidebarWidth(newWidth);
    };

    const onMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };

    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
  }

  return (
    <div className="w-full relative">
      {/* Loading State - Positioned as overlay */}
      {isLoading && <LoadingOverlay />}

      {/* Error State */}
      {error && (
        <ErrorNotification
          message={t('common.errorFetchingData')}
          error={error}
          onRetry={refetch}
        />
      )}

      <div className="flex flex-wrap justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">{t('companies.companiesList')}</h1>
        {/* Only show Add button for Administrators */}
        {currentUser?.role === 'Administrator' && (
          <button
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center"
            onClick={() => setIsCreateModalOpen(true)}
            disabled={createCompany.isPending}
          >
            {createCompany.isPending ? (
              <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
              </svg>
            )}
            {t('companies.addNewCompany')}
          </button>
        )}
      </div>

      {/* Search and filters */}
      <div className="bg-white dark:bg-gray-900 p-4 rounded-lg shadow mb-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div className="relative flex-1 min-w-0">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 dark:text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
              </svg>
            </div>
            <input
              type="text"
              className="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-200 text-sm rounded-lg block w-full pl-10 p-2.5"
              placeholder={t('companies.searchCompanies')}
              value={searchQuery}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleSearchChange(e.target.value)}
            />
          </div>

          <div className="flex space-x-4 flex-shrink-0">
            <select
              className="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-200 text-sm rounded-lg p-2.5 w-36"
              value={filterIndustry}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => handleFilterIndustryChange(e.target.value)}
            >
              <option value="all">All Industries</option>
              <option value="shipping">Shipping</option>
              <option value="cargo">Cargo</option>
              <option value="oil & gas">Oil & Gas</option>
              <option value="logistics">Logistics</option>
              <option value="container shipping">Container Shipping</option>
            </select>

            <select
              className="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-200 text-sm rounded-lg p-2.5 w-36"
              value={filterStatus}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => handleFilterStatusChange(e.target.value)}
            >
              <option value="all">All Statuses</option>
              <option value="active">Active</option>
              <option value="maintenance">Maintenance</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>
      </div>

      {/* Companies list */}
      <div className="bg-white dark:bg-gray-900 rounded-lg shadow">
        <div className="w-full overflow-x-auto">
          <table className="w-full divide-y divide-gray-200 dark:divide-gray-700 table-fixed">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-1/4">
                  {t('common.name')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-1/6">
                  {t('companies.location')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-1/6">
                  {t('companies.industry')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-[10%]">
                  {t('common.status')}
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-[12%]">
                  {t('common.actions')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              {companies.map((company) => (
                <CompanyRow
                  key={company.id}
                  company={company}
                  refetch={refetch}
                />
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {companies.length > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={metadata.total || 0}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
          />
        )}

        {/* Empty state */}
        {companies.length === 0 && (
          <div className="p-8 text-center">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">{t('companies.noCompaniesFound')}</h3>

            {currentUser?.role === 'Administrator' ? (
              <>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  {t('companies.noCompaniesMessage')}
                </p>
                <button
                  className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center mx-auto"
                  onClick={() => setIsCreateModalOpen(true)}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                  </svg>
                  {t('companies.addNewCompany')}
                </button>
              </>
            ) : (
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {t('companies.noCompaniesMessageUser')}
              </p>
            )}
          </div>
        )}
      </div>

      {/* Create Company Slideover */}
      <div
        className={`fixed inset-y-0 right-0 bg-white dark:bg-gray-900 shadow-xl transform transition-all duration-300 ease-in-out z-50 ${
          isCreateModalOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
        style={{ width: `${sidebarWidth}%` }}
      >
        {/* Resize handle */}
        <div
          className="absolute top-0 bottom-0 -left-1 w-2 cursor-col-resize"
          onMouseDown={startResize}
        >
          <div className="absolute top-1/2 transform -translate-y-1/2 -left-3 w-6 h-12 bg-blue-500 hover:bg-blue-600 rounded-l-md flex items-center justify-center cursor-col-resize group transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
            </svg>
            <span className="absolute left-0 ml-8 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              {t('common.dragToResize')}
            </span>
          </div>
        </div>

        {/* Indicator for when dragging is active */}
        {isDragging && (
          <div className="fixed inset-0 cursor-col-resize z-40" />
        )}

        <div className="h-full">
          {isCreateModalOpen && (
            <EditCompanyForm
              company={null}
              onSave={handleAddCompany}
              onCancel={() => setIsCreateModalOpen(false)}
            />
          )}
        </div>
      </div>

      {/* Backdrop */}
      {isCreateModalOpen && (
        <div
          className="fixed inset-0 bg-gray-600 bg-opacity-50 z-40"
          onClick={() => setIsCreateModalOpen(false)}
        ></div>
      )}
    </div>
  );
};

export default Companies;