import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { ProfileNavigationProps, ProfileTab } from './types';

const ProfileNavigation: React.FC<ProfileNavigationProps> = ({
  activeTab,
  setActiveTab,
  userRole
}) => {
  const { t } = useTranslation();
  
  // Define tab configuration
  const tabs: { id: ProfileTab; label: string; showFor: string[] }[] = [
    // Removed 'profile' tab as per user request
    { id: 'subscriptions', label: t('common.subscriptions'), showFor: ['Customer', 'Manager', 'Technician', 'Viewer'] },
    { id: 'security', label: t('profile.accountSettings'), showFor: ['Administrator', 'Customer', 'Manager', 'Technician', 'Viewer'] },
    { id: 'notifications', label: t('profile.notifications'), showFor: ['Administrator', 'Customer', 'Manager', 'Technician', 'Viewer'] },
    { id: 'language', label: t('profile.language'), showFor: ['Administrator', 'Customer', 'Manager', 'Technician', 'Viewer'] },
    { id: 'emailProcessing', label: t('emailProcessing.title'), showFor: ['Administrator'] },
    { id: 'wlpTemplates', label: t('wlpTemplates.title'), showFor: ['Administrator'] }
  ];

  // Filter tabs by user role
  const visibleTabs = tabs.filter(tab => tab.showFor.includes(userRole));

  return (
    <div className="border-b border-gray-200 dark:border-gray-700">
      <nav className="px-8 flex space-x-8 overflow-x-auto">
        {visibleTabs.map(tab => (
          <Link
            key={tab.id}
            to={`/profile/${tab.id}`}
            className={`py-4 font-medium text-sm border-b-2 whitespace-nowrap ${
              activeTab === tab.id
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
            }`}
          >
            {tab.label}
          </Link>
        ))}
      </nav>
    </div>
  );
};

export default ProfileNavigation;