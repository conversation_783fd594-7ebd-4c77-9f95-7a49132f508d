import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import wlpTemplateService from '../../../services/wlpTemplateService';

interface WlpTemplateSettingsProps {
  userData: any;
  isEditing: boolean;
}

interface WlpTemplate {
  id: string;
  name: string;
  description?: string;
  fileName: string;
  originalFilename: string;
  uploadTimestamp: string;
  fileSize: number;
  zoneCount: number;
  zoneNames: string[];
  created: string;
  createdByName: string;
  uploaderName: string;
}

interface CreateWlpTemplate {
  name: string;
  description?: string;
  fileName: string;
  originalFilename?: string;
  fileContent: ArrayBuffer;
}

const WlpTemplateSettings: React.FC<WlpTemplateSettingsProps> = ({
  userData,
  isEditing
}) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Local state
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [uploadSuccess, setUploadSuccess] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [showUploadForm, setShowUploadForm] = useState(false);
  const [templateForm, setTemplateForm] = useState({
    name: '',
    description: ''
  });

  // React Query - Get templates
  const { data: templates, isLoading, error } = useQuery({
    queryKey: ['wlpTemplates'],
    queryFn: () => wlpTemplateService.getTemplates({}),
    enabled: !!userData?.id
  });

  // React Query - Delete template
  const deleteTemplateMutation = useMutation({
    mutationFn: (templateId: string) => wlpTemplateService.deleteTemplate(templateId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['wlpTemplates'] });
      setUploadSuccess(t('wlpTemplates.deleteSuccess'));
      setTimeout(() => setUploadSuccess(null), 3000);
    },
    onError: (error: any) => {
      setUploadError(error.message || t('wlpTemplates.deleteError'));
      setTimeout(() => setUploadError(null), 5000);
    }
  });

  // React Query - Create template
  const createTemplateMutation = useMutation({
    mutationFn: (template: CreateWlpTemplate) => wlpTemplateService.createTemplate(template),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['wlpTemplates'] });
      setUploadSuccess(t('wlpTemplates.uploadSuccess'));
      setShowUploadForm(false);
      setTemplateForm({ name: '', description: '' });
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      setTimeout(() => setUploadSuccess(null), 3000);
    },
    onError: (error: any) => {
      setUploadError(error.message || t('wlpTemplates.uploadError'));
      setTimeout(() => setUploadError(null), 5000);
    },
    onSettled: () => {
      setIsUploading(false);
    }
  });

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.name.toLowerCase().endsWith('.wlp')) {
      setUploadError(t('wlpTemplates.invalidFileType'));
      setTimeout(() => setUploadError(null), 5000);
      return;
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      setUploadError(t('wlpTemplates.fileTooLarge'));
      setTimeout(() => setUploadError(null), 5000);
      return;
    }

    // Validate form
    if (!templateForm.name.trim()) {
      setUploadError(t('wlpTemplates.nameRequired'));
      setTimeout(() => setUploadError(null), 5000);
      return;
    }

    setIsUploading(true);
    setUploadError(null);

    try {
      const arrayBuffer = await file.arrayBuffer();
      
      const createRequest: CreateWlpTemplate = {
        name: templateForm.name.trim(),
        description: templateForm.description.trim() || undefined,
        fileName: file.name,
        originalFilename: file.name,
        fileContent: arrayBuffer
      };

      createTemplateMutation.mutate(createRequest);
    } catch (error) {
      console.error('Error reading file:', error);
      setUploadError(t('wlpTemplates.fileReadError'));
      setIsUploading(false);
      setTimeout(() => setUploadError(null), 5000);
    }
  };

  const handleDeleteTemplate = (templateId: string, templateName: string) => {
    if (window.confirm(t('wlpTemplates.confirmDelete', { name: templateName }))) {
      deleteTemplateMutation.mutate(templateId);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const formatDateTime = (dateString: string): string => {
    return new Date(dateString).toLocaleString();
  };

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-3 text-gray-600 dark:text-gray-400">
            {t('common.loading')}
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
      {/* Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              {t('wlpTemplates.title')}
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {t('wlpTemplates.description')}
            </p>
          </div>
          <button
            onClick={() => setShowUploadForm(!showUploadForm)}
            className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
          >
            {showUploadForm ? t('common.cancel') : t('wlpTemplates.uploadTemplate')}
          </button>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Upload Form */}
        {showUploadForm && (
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6 bg-gray-50 dark:bg-gray-750">
            <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
              {t('wlpTemplates.uploadNewTemplate')}
            </h4>
            
            <div className="space-y-4">
              <div>
                <label htmlFor="templateName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('wlpTemplates.templateName')} *
                </label>
                <input
                  type="text"
                  id="templateName"
                  value={templateForm.name}
                  onChange={(e) => setTemplateForm({ ...templateForm, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder={t('wlpTemplates.templateNamePlaceholder')}
                  disabled={isUploading}
                />
              </div>

              <div>
                <label htmlFor="templateDescription" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('wlpTemplates.templateDescription')}
                </label>
                <textarea
                  id="templateDescription"
                  value={templateForm.description}
                  onChange={(e) => setTemplateForm({ ...templateForm, description: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder={t('wlpTemplates.templateDescriptionPlaceholder')}
                  disabled={isUploading}
                />
              </div>

              <div>
                <label htmlFor="wlpFile" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('wlpTemplates.selectWlpFile')} *
                </label>
                <input
                  ref={fileInputRef}
                  type="file"
                  id="wlpFile"
                  accept=".wlp"
                  onChange={handleFileUpload}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white file:mr-4 file:py-1 file:px-2 file:rounded file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 dark:file:bg-blue-900 dark:file:text-blue-300"
                  disabled={isUploading}
                />
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  {t('wlpTemplates.fileRequirements')}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Status Messages */}
        {uploadError && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-800 dark:text-red-200">{uploadError}</p>
              </div>
            </div>
          </div>
        )}

        {uploadSuccess && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-green-800 dark:text-green-200">{uploadSuccess}</p>
              </div>
            </div>
          </div>
        )}

        {/* Upload Progress */}
        {isUploading && (
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
              </div>
              <div className="ml-3">
                <p className="text-sm text-blue-800 dark:text-blue-200">
                  {t('wlpTemplates.uploadingTemplate')}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Templates List */}
        <div>
          <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
            {t('wlpTemplates.existingTemplates')}
          </h4>

          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4 mb-4">
              <p className="text-sm text-red-800 dark:text-red-200">
                {t('wlpTemplates.loadError')}
              </p>
            </div>
          )}

          {templates?.length === 0 ? (
            <div className="text-center py-12">
              <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                {t('wlpTemplates.noTemplates')}
              </h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {t('wlpTemplates.noTemplatesDescription')}
              </p>
            </div>
          ) : (
            <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg">
              <table className="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-900">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      {t('wlpTemplates.templateName')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      {t('wlpTemplates.zones')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      {t('wlpTemplates.fileInfo')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      {t('wlpTemplates.uploaded')}
                    </th>
                    <th className="relative px-6 py-3">
                      <span className="sr-only">{t('common.actions')}</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {templates?.map((template: WlpTemplate) => (
                    <tr key={template.id} className="hover:bg-gray-50 dark:hover:bg-gray-750">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {template.name}
                          </div>
                          {template.description && (
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {template.description}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {template.zoneCount} {t('wlpTemplates.zones')}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {template.zoneNames.slice(0, 3).join(', ')}
                          {template.zoneNames.length > 3 && '...'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {template.originalFilename}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {formatFileSize(template.fileSize)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {formatDateTime(template.created)}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {t('wlpTemplates.by')} {template.createdByName}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => handleDeleteTemplate(template.id, template.name)}
                          disabled={deleteTemplateMutation.isPending}
                          className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {deleteTemplateMutation.isPending ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-500"></div>
                          ) : (
                            t('common.delete')
                          )}
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default WlpTemplateSettings;