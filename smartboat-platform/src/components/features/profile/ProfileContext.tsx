import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { useAuth } from "../../../context/AuthContext";
import { User, UserRole, Subscription } from "../../../types";
import { ProfileContextType } from "./types";
import {
  useUser,
  useUpdateUser,
  useUserNotificationSettings,
  useUpdateUserNotificationSettings,
  useUpdateUserRole,
} from "../../../hooks/queries/useUserQueries";
import { useSubscriptionsByCustomer } from "../../../hooks/queries/useSubscriptionQueries";
import { LoadingOverlay, ErrorNotification } from "../../common";

// Simplified context without tab management
const initialContextValue: ProfileContextType = {
  userData: null,
  isEditing: false,
  userSubscriptions: [],
  isLoading: false,
  error: null,
  setUserData: () => {},
  setIsEditing: () => {},
  handleChange: () => {},
  handleSubmit: () => {},
  getInitials: () => "",
  refetchData: () => {},
  refetchNotifications: () => {},
  refetchSubscriptions: () => {},
};

// Create context with default values
const ProfileContext = createContext<ProfileContextType>(initialContextValue);

// Custom hook to use profile context
export const useProfile = () => {
  const context = useContext(ProfileContext);
  if (context === undefined) {
    throw new Error("useProfile must be used within a ProfileProvider");
  }
  return context;
};

interface ProfileProviderProps {
  children: ReactNode;
}

export const ProfileProvider: React.FC<ProfileProviderProps> = ({
  children,
}) => {
  // Core state
  const [isEditing, setIsEditing] = useState(false);
  const { currentUser, updateUserRole } = useAuth();
  const [userData, setUserData] = useState<User | null>(
    currentUser ? {
      ...currentUser,
      notifications: currentUser.notifications || {
        email: false,
        push: false,
        sms: false
      }
    } : null
  );
  const [userSubscriptions, setUserSubscriptions] = useState<Subscription[]>(
    []
  );

  // Fetch user data using React Query
  const {
    data: userDetails,
    isLoading: isLoadingUser,
    isError: isErrorUser,
    error: userError,
    refetch: refetchUser,
  } = useUser(currentUser?.id, {
    initialData: currentUser,
    enabled: !!currentUser?.id,
    useMockFallback: true,
  });

  // Update mutation for user data
  const updateUser = useUpdateUser();

  // Update mutation for user role changes
  const updateUserRoleMutation = useUpdateUserRole();

  // Fetch user notification settings using React Query
  const {
    isLoading: isLoadingNotifications,
    isError: isErrorNotifications,
    refetch: refetchNotifications,
  } = useUserNotificationSettings(currentUser?.id, {
    enabled: !!currentUser?.id,
    useMockFallback: true,
  });

  // Update notification settings mutation
  const updateNotificationSettings = useUpdateUserNotificationSettings();

  // Fetch user subscriptions using React Query
  const {
    data: subscriptions = [],
    isLoading: isLoadingSubscriptions,
    isError: isErrorSubscriptions,
    refetch: refetchSubscriptions,
  } = useSubscriptionsByCustomer(currentUser?.customerId, {
    enabled: !!currentUser?.customerId && currentUser?.role !== "Administrator",
    useMockFallback: true,
  });

  // Combined loading state
  const isLoading =
    isLoadingUser ||
    isLoadingNotifications ||
    isLoadingSubscriptions ||
    updateUser.isPending ||
    updateUserRoleMutation.isPending ||
    updateNotificationSettings.isPending;

  // Combined error state
  const error =
    userError ||
    (isErrorNotifications || isErrorSubscriptions
      ? new Error("Failed to load user data")
      : null);

  // Function to refetch all data
  const refetchData = () => {
    refetchUser();
    refetchNotifications();
    refetchSubscriptions();
  };

  // Update user data when API response comes back
  useEffect(() => {
    if (userDetails) {
      // Ensure notifications object exists
      const updatedUserDetails = {
        ...userDetails,
        notifications: userDetails.notifications || {
          email: false,
          push: false,
          sms: false
        }
      };
      setUserData(updatedUserDetails);
    }
  }, [userDetails]);

  // Handle form input changes
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    if (!userData) return;

    const { name, value, type } = e.target;

    if (type === "checkbox") {
      const checkbox = e.target as HTMLInputElement;
      const notificationKey = name.split(".")[1];

      // Update local state first for responsive UI
      setUserData({
        ...userData,
        notifications: {
          ...(userData.notifications || { email: false, push: false, sms: false }),
          [notificationKey]: checkbox.checked,
        },
      });

      // If this is a notification preference, update via API
      if (name.startsWith("notifications.") && userData.id) {
        const updatedSettings = {
          ...(userData.notifications || { email: false, push: false, sms: false }),
          [notificationKey]: checkbox.checked,
        };

        updateNotificationSettings.mutate(
          {
            id: userData.id,
            notificationSettings: updatedSettings,
          },
          {
            onError: (error) => {
              console.error("Failed to update notification settings:", error);
              // Revert the local state change if the API update fails
              refetchNotifications();
            },
          }
        );
      }
    } else if (name.includes(".")) {
      // Handle nested attributes (like socialLinks.linkedin)
      const [parent, child] = name.split(".");
      setUserData({
        ...userData,
        [parent]: {
          ...userData[parent as keyof typeof userData],
          [child]: value,
        },
      });
    } else if (name === "role") {
      // Handle role changes - in a real app this would be restricted
      const newRole = value as UserRole;

      if (userData.id) {
        // Update the role using the API service
        updateUserRoleMutation.mutate(
          { id: userData.id, role: newRole },
          {
            onSuccess: () => {
              setUserData({
                ...userData,
                role: newRole,
              });

              // Update the role in the auth context (for testing/development)
              updateUserRole(newRole);
            },
            onError: (error) => {
              console.error("Failed to update role:", error);
            },
          }
        );
      } else {
        // Fallback for when userData.id is not available
        setUserData({
          ...userData,
          role: newRole,
        });
        updateUserRole(newRole);
      }
    } else {
      setUserData({
        ...userData,
        [name]: value,
      });
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!userData) return;

    updateUser.mutate(
      { id: userData.id, userData },
      {
        onSuccess: () => {
          setIsEditing(false);
          // We should use a proper notification system instead of alert in a production app
          console.log("Profile updated successfully");
        },
        onError: (error) => {
          console.error("Failed to update profile:", error);
          // Could display an error notification here
        },
      }
    );
  };

  // Generate user initials for avatar fallback (robust to missing/empty names)
  const getInitials = (name?: string | null) => {
    if (!name || typeof name !== "string") return "";
    const cleaned = name.trim();
    if (!cleaned) return "";
    const parts = cleaned.split(/\s+/).filter(Boolean);
    if (parts.length >= 2) {
      return (parts[0][0] + parts[1][0]).toUpperCase();
    }
    // Single word name: use up to first two letters
    return parts[0].slice(0, 2).toUpperCase();
  };

  // Create the context value object - REMOVED all tab-related state and functions
  const contextValue: ProfileContextType = {
    userData,
    isEditing,
    userSubscriptions,
    isLoading,
    error,
    setUserData,
    setIsEditing,
    handleChange,
    handleSubmit,
    getInitials,
    refetchData,
    refetchNotifications,
    refetchSubscriptions,
  };

  return (
    <ProfileContext.Provider value={contextValue}>
      {isLoading && <LoadingOverlay />}
      {error && (
        <ErrorNotification
          message="Failed to load profile data"
          error={error}
          onRetry={refetchData}
        />
      )}
      {children}
    </ProfileContext.Provider>
  );
};

export default ProfileContext;
