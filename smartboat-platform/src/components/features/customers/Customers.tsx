import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Customer } from '../../../types';
import { useCustomers } from '../../../hooks/queries/useCustomerQueries';
import CustomerRow from './CustomerRow';
import EditCustomerForm from './EditCustomerForm';
import { LoadingOverlay, ErrorNotification, Pagination } from '../../common';

const Customers: React.FC = () => {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [sidebarWidth, setSidebarWidth] = useState(30); // Width in percentage
  const [isDragging, setIsDragging] = useState(false); // Add state for dragging

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);

  // Calculate page offset (backend uses 0-based offset)
  const pageOffset = (currentPage - 1) * itemsPerPage;

  // Query parameters with pagination
  const queryOptions = {
    // Pagination parameters
    pageLimit: itemsPerPage,
    pageOffset: pageOffset,
    sortField: 'name',
    sortOrder: 'ASC',
    // Search parameters
    ...(searchQuery && { searchTerm: searchQuery }),
    // Fallback to mock data if API fails
    useMockFallback: true
  };

  // Fetch customers data using React Query
  const {
    data: customersResponse,
    isLoading,
    isError,
    error,
    refetch
  } = useCustomers(queryOptions);

  // Extract data and metadata from response
  const customers = customersResponse?.data || [];
  const metadata = customersResponse?.metadata || { pageLimit: itemsPerPage, pageOffset: pageOffset, total: 0 };

  // Calculate pagination values
  const totalPages = Math.ceil((metadata.total || 0) / itemsPerPage);

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Handle search changes (reset to first page)
  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    setCurrentPage(1);
  };

  // Handle adding a new customer (only close modal, form handles the creation)
  const handleAddCustomer = (newCustomer: Customer) => {
    setIsCreateModalOpen(false);
  };

  // Improved resize handler with isDragging state
  function startResize(e: React.MouseEvent) {
    e.preventDefault();
    setIsDragging(true);

    // Define handlers inline to avoid hook issues
    const onMouseMove = (moveEvent: MouseEvent) => {
      // Calculate new width based on mouse position
      const windowWidth = window.innerWidth;
      const mouseX = moveEvent.clientX;

      // Convert to percentage (100 - x because we're dragging from right side)
      const widthPercent = 100 - (mouseX / windowWidth * 100);

      // Limit to reasonable values (between 20% and 80%)
      const newWidth = Math.max(20, Math.min(80, widthPercent));
      setSidebarWidth(newWidth);
    };

    const onMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };

    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
  }

  return (
    <div className="container mx-auto">
      {/* Loading overlay */}
      {isLoading && <LoadingOverlay />}

      {/* Error notification */}
      {isError && (
        <ErrorNotification
          message={t('common.errorFetchingCustomers')}
          onRetry={refetch}
          error={error}
        />
      )}

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">{t('customers.customersList')}</h1>
        <button
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center"
          onClick={() => setIsCreateModalOpen(true)}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
          </svg>
          {t('customers.addNewCustomer')}
        </button>
      </div>

      {/* Search bar */}
      <div className="bg-white dark:bg-gray-900 p-4 rounded-lg shadow mb-6">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 dark:text-gray-500" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
            </svg>
          </div>
          <input
            type="text"
            className="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-200 text-sm rounded-lg block w-full pl-10 p-2.5"
            placeholder={t('customers.searchCustomers')}
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
          />
        </div>
      </div>

      {/* Customer list */}
      <div className="bg-white dark:bg-gray-900 rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {t('customers.customerName')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {t('common.contact')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {t('common.status')}
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {t('common.actions')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              {customers.map((customer) => (
                <CustomerRow
                  key={customer.id}
                  customer={customer}
                  refetch={refetch}
                />
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {customers.length > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={metadata.total || 0}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
          />
        )}

        {/* Empty state */}
        {!isLoading && customers.length === 0 && (
          <div className="p-8 text-center">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">{t('customers.noCustomersFound')}</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              {searchQuery
                ? t('customers.noSearchResults')
                : t('customers.noCustomersMessage')}
            </p>
            {!searchQuery && (
              <button
                className="mt-2 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
                onClick={() => setIsCreateModalOpen(true)}
              >
                {t('customers.addFirstCustomer')}
              </button>
            )}
          </div>
        )}
      </div>

      {/* Create Customer Slideover */}
      <div
        className={`fixed inset-y-0 right-0 bg-white dark:bg-gray-900 shadow-xl transform transition-all duration-300 ease-in-out z-50 ${
          isCreateModalOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
        style={{ width: `${sidebarWidth}%` }}
      >
        {/* Resize handle */}
        <div
          className="absolute top-0 bottom-0 -left-1 w-2 cursor-col-resize"
          onMouseDown={startResize}
        >
          <div className="absolute top-1/2 transform -translate-y-1/2 -left-3 w-6 h-12 bg-blue-500 hover:bg-blue-600 rounded-l-md flex items-center justify-center cursor-col-resize group transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
            </svg>
            <span className="absolute left-0 ml-8 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              {t('common.dragToResize')}
            </span>
          </div>
        </div>

        {/* Indicator for when dragging is active */}
        {isDragging && (
          <div className="fixed inset-0 cursor-col-resize z-40" />
        )}

        <div className="h-full">
          {isCreateModalOpen && (
            <EditCustomerForm
              customer={null}
              onSave={handleAddCustomer}
              onCancel={() => setIsCreateModalOpen(false)}
            />
          )}
        </div>
      </div>

      {/* Backdrop */}
      {isCreateModalOpen && (
        <div
          className="fixed inset-0 bg-gray-600 bg-opacity-50 z-40"
          onClick={() => setIsCreateModalOpen(false)}
        ></div>
      )}
    </div>
  );
};

export default Customers;