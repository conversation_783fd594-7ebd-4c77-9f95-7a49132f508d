import React, { useState, useEffect } from 'react';
import { Sensor } from '../../../types';
import { useTranslation } from 'react-i18next';
import { useCreateSensor, useUpdateSensor } from '../../../hooks/queries/useSensorQueries';
import { useVessels } from '../../../hooks/queries/useVesselQueries';

interface EditSensorFormProps {
  sensor: Sensor | null; // The sensor to edit, null if creating a new one
  onSave: (sensor: Sensor) => void;
  onCancel: () => void;
  preselectedVesselId?: string; // Optional vessel ID to preselect when creating new sensor
}

const EditSensorForm: React.FC<EditSensorFormProps> = ({
  sensor,
  onSave,
  onCancel,
  preselectedVesselId
}) => {
  const { t } = useTranslation();

  // Setup React Query mutations
  const createSensor = useCreateSensor();
  const updateSensor = useUpdateSensor();

  // Fetch vessels for dropdown
  const { data: vesselsResponse, isLoading: vesselsLoading } = useVessels();
  const vessels = vesselsResponse?.data || [];

  // Submission state tracking
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form state
  const [formData, setFormData] = useState<Partial<Sensor>>({
    name: '',
    type: 'Temperature',
    vesselId: '',
    location: '',
    status: 'Active',
    alertThreshold: '',
  });

  // Active section state
  const [activeSection, setActiveSection] = useState('basic');

  // Initialize form data when sensor changes
  useEffect(() => {
    if (sensor) {
      setFormData({
        id: sensor.id,
        name: sensor.name,
        type: sensor.type,
        vesselId: sensor.vesselId,
        location: sensor.location,
        status: sensor.status,
        alertThreshold: sensor.alertThreshold,
        // Keep the following fields as they are (not editable directly)
        lastReading: sensor.lastReading,
        lastUpdated: sensor.lastUpdated
      });
    } else {
      // Reset form if not editing (creating new)
      setFormData({
        name: '',
        type: 'Temperature',
        vesselId: preselectedVesselId || '',
        location: '',
        status: 'Active',
        alertThreshold: '',
      });
    }
  }, [sensor, preselectedVesselId]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Prevent double submission
    if (isSubmitting || createSensor.isPending || updateSensor.isPending) {
      return;
    }

    setIsSubmitting(true);

    // Validate form data - backend requires name, type, and vesselId
    if (!formData.name || !formData.type || !formData.vesselId) {
      alert(t('sensors.fillAllRequired'));
      setIsSubmitting(false);
      return;
    }

    // Create the sensor data for API (matching CreateSensorDto structure)
    const sensorDataForAPI = {
      name: formData.name || '',
      type: formData.type || 'Temperature',
      vesselId: formData.vesselId || '',
      location: formData.location || '',
      status: formData.status || 'Active',
      alertThreshold: formData.alertThreshold || '',
    };

    // Create a complete sensor object for UI state management
    const completeSensor: Sensor = {
      id: formData.id || Math.floor(Math.random() * 1000).toString(), // Generate random ID for new sensors (API would assign real IDs)
      name: formData.name || '',
      type: formData.type || 'Temperature',
      vesselId: formData.vesselId || '',
      location: formData.location || '',
      status: formData.status || 'Active',
      alertThreshold: formData.alertThreshold || '',
      lastReading: formData.lastReading || '0.0',
      lastUpdated: formData.lastUpdated || 'Just now',
    };

    // If we're editing an existing sensor
    if (sensor) {
      updateSensor.mutate({
        id: completeSensor.id,
        sensorData: sensorDataForAPI
      }, {
        onSuccess: (data) => {
          // Call parent handler and close form
          onSave(data || completeSensor);
          setIsSubmitting(false);
        },
        onError: (error) => {
          console.error('Failed to update sensor:', error);
          setIsSubmitting(false);
          // You could show an error notification here
        }
      });
    } else {
      // Creating a new sensor
      createSensor.mutate(sensorDataForAPI, {
        onSuccess: (data) => {
          // Call parent handler and close form
          onSave(data || completeSensor);
          setIsSubmitting(false);
        },
        onError: (error) => {
          console.error('Failed to create sensor:', error);
          setIsSubmitting(false);
          // You could show an error notification here
        }
      });
    }
  };

  // Get default threshold based on selected type
  const getDefaultThreshold = (type: string) => {
    switch (type) {
      case 'Temperature':
        return '45°C';
      case 'Pressure':
        return '3.0 MPa';
      case 'Humidity':
        return '80%';
      case 'Flow Rate':
        return '600 L/h';
      default:
        return '';
    }
  };

  // Block types for the form sections
  const blockTypes = [
    { id: 'basic', label: t('sensors.basicInformation'), icon: '/assets/icons/sensor.svg', color: 'bg-blue-500' },
    { id: 'configuration', label: t('sensors.configuration'), icon: '/assets/icons/settings.svg', color: 'bg-green-500' },
    { id: 'readings', label: t('sensors.readings'), icon: '/assets/icons/chart.svg', color: 'bg-purple-500' },
  ];

  // Function to render block icon
  const renderBlockIcon = (blockType: string) => {
    switch (blockType) {
      case 'basic':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
          </svg>
        );
      case 'configuration':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        );
      case 'readings':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        );
      default:
        return null;
    }
  };

  // Function to get block color
  const getBlockColor = (blockType: string) => {
    const block = blockTypes.find(b => b.id === blockType);
    return block ? block.color : 'bg-gray-500';
  };

  return (
    <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg overflow-hidden flex flex-col h-full">
      <div className="flex flex-col md:flex-row flex-1 overflow-hidden">
        {/* Left sidebar - Block selection */}
        <div className="w-full md:w-56 bg-gray-900 border-r border-gray-700 p-3 flex flex-col">
          <div className="mb-4">
            <h2 className="text-lg font-semibold text-white">
              {sensor ? t('sensors.editSensor') : t('sensors.createNewSensor')}
            </h2>
            <p className="text-xs text-gray-400 mt-1">
              {sensor ? t('sensors.updateSensorInfo') : t('sensors.addNewSensorInfo')}
            </p>
          </div>

          <div className="mb-4">
            <h3 className="text-xs uppercase tracking-wider text-gray-400 font-semibold mb-2">{t('sensors.blocks')}</h3>
            <div className="space-y-2">
              {blockTypes.map(block => (
                <button
                  key={block.id}
                  onClick={() => setActiveSection(block.id)}
                  className={`w-full flex items-center p-2 rounded-md transition-colors ${
                    activeSection === block.id
                      ? block.color
                      : 'bg-gray-800 hover:bg-gray-700'
                  } text-white`}
                >
                  <div className={`w-8 h-8 rounded-md flex items-center justify-center ${
                    activeSection === block.id ? 'bg-white bg-opacity-20' : block.color
                  } mr-2`}>
                    {renderBlockIcon(block.id)}
                  </div>
                  <span className="text-sm font-medium">{block.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Main content area */}
        <div className="flex-1 flex flex-col bg-gray-900 overflow-hidden">
          <div className="flex-1 overflow-y-auto p-6">
            <form id="sensor-form" onSubmit={handleSubmit} className="max-w-3xl mx-auto">
            <div className="mb-4 flex items-center justify-center">
              <h3 className="text-base font-medium text-white">
                {activeSection === 'basic' ? t('sensors.basicInformation') :
                 activeSection === 'configuration' ? t('sensors.configuration') : t('sensors.readingsData')}
              </h3>
              <div className={`ml-2 w-2 h-2 rounded-full ${getBlockColor(activeSection)}`}></div>
            </div>

            {/* Basic Information Section */}
            {activeSection === 'basic' && (
              <div className="bg-gray-800 rounded-lg border border-gray-700 shadow-sm hover:shadow-md transition-shadow">
                <div className="p-4 border-b border-gray-700 flex items-center">
                  <div className={`w-8 h-8 ${getBlockColor('basic')} rounded-md flex items-center justify-center text-white mr-3`}>
                    {renderBlockIcon('basic')}
                  </div>
                  <h4 className="text-md font-medium text-gray-200">{t('sensors.basicInformation')}</h4>
                </div>
                <div className="p-5">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Sensor Name */}
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('sensors.name')}*
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name || ''}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        required
                      />
                    </div>

                    {/* Sensor Type */}
                    <div>
                      <label htmlFor="type" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('sensors.type')}*
                      </label>
                      <select
                        id="type"
                        name="type"
                        value={formData.type || 'Temperature'}
                        onChange={(e) => {
                          const newType = e.target.value;
                          // Update the alert threshold with a default value based on the type
                          setFormData(prev => ({
                            ...prev,
                            type: newType,
                            alertThreshold: prev.alertThreshold || getDefaultThreshold(newType)
                          }));
                        }}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      >
                        <option value="Temperature">{t('sensors.types.temperature')}</option>
                        <option value="Pressure">{t('sensors.types.pressure')}</option>
                        <option value="Humidity">{t('sensors.types.humidity')}</option>
                        <option value="Flow Rate">{t('sensors.types.flowRate')}</option>
                      </select>
                    </div>

                    {/* Vessel */}
                    <div>
                      <label htmlFor="vesselId" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('sensors.vessel')}*
                      </label>
                      <select
                        id="vesselId"
                        name="vesselId"
                        value={formData.vesselId || ''}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        required
                        disabled={vesselsLoading}
                      >
                        <option value="">{vesselsLoading ? t('common.loading') : t('sensors.selectVessel')}</option>
                        {vessels.map((vessel) => (
                          <option key={vessel.id} value={vessel.id}>
                            {vessel.name} - {vessel.type || 'Unknown Type'}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Location */}
                    <div>
                      <label htmlFor="location" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('sensors.location')}*
                      </label>
                      <input
                        type="text"
                        id="location"
                        name="location"
                        value={formData.location || ''}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        required
                        placeholder="e.g. Engine Room, Deck, etc."
                      />
                    </div>

                    {/* Status */}
                    <div>
                      <label htmlFor="status" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('common.status')}
                      </label>
                      <select
                        id="status"
                        name="status"
                        value={formData.status || 'Active'}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      >
                        <option value="Active">{t('common.active')}</option>
                        <option value="Warning">{t('sensors.status.warning')}</option>
                        <option value="Critical">{t('sensors.status.critical')}</option>
                        <option value="Inactive">{t('common.inactive')}</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Configuration Section */}
            {activeSection === 'configuration' && (
              <div className="bg-gray-800 rounded-lg border border-gray-700 shadow-sm hover:shadow-md transition-shadow">
                <div className="p-4 border-b border-gray-700 flex items-center">
                  <div className={`w-8 h-8 ${getBlockColor('configuration')} rounded-md flex items-center justify-center text-white mr-3`}>
                    {renderBlockIcon('configuration')}
                  </div>
                  <h4 className="text-md font-medium text-gray-200">{t('sensors.configuration')}</h4>
                </div>
                <div className="p-5">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Alert Threshold */}
                    <div>
                      <label htmlFor="alertThreshold" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('sensors.alertThreshold')}
                      </label>
                      <input
                        type="text"
                        id="alertThreshold"
                        name="alertThreshold"
                        value={formData.alertThreshold || getDefaultThreshold(formData.type || 'Temperature')}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder={getDefaultThreshold(formData.type || 'Temperature')}
                      />
                      <p className="mt-1 text-xs text-gray-400">
                        {formData.type === 'Temperature' ? 'Enter value in °C (e.g. 45°C)' :
                         formData.type === 'Pressure' ? 'Enter value in MPa (e.g. 3.0 MPa)' :
                         formData.type === 'Humidity' ? 'Enter value in % (e.g. 80%)' :
                         formData.type === 'Flow Rate' ? 'Enter value in L/h (e.g. 600 L/h)' :
                         'Enter threshold value'}
                      </p>
                    </div>
                  </div>

                  {/* Configuration options placeholder */}
                  <div className="mt-6 bg-gray-700 rounded-lg p-4 border border-gray-600 h-48 flex items-center justify-center">
                    <div className="text-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      <p className="text-gray-400 text-sm">Advanced configuration options will be available in future updates</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Readings Section */}
            {activeSection === 'readings' && (
              <div className="bg-gray-800 rounded-lg border border-gray-700 shadow-sm hover:shadow-md transition-shadow">
                <div className="p-4 border-b border-gray-700 flex items-center">
                  <div className={`w-8 h-8 ${getBlockColor('readings')} rounded-md flex items-center justify-center text-white mr-3`}>
                    {renderBlockIcon('readings')}
                  </div>
                  <h4 className="text-md font-medium text-gray-200">{t('sensors.readingsData')}</h4>
                </div>
                <div className="p-5">
                  {sensor ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          {t('sensors.lastReading')}
                        </label>
                        <input
                          type="text"
                          value={formData.lastReading || '0.0'}
                          className="block w-full px-3 py-2 border border-gray-600 rounded-md shadow-sm bg-gray-700 text-gray-300 sm:text-sm"
                          disabled
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          {t('sensors.lastUpdated')}
                        </label>
                        <input
                          type="text"
                          value={formData.lastUpdated || 'Never'}
                          className="block w-full px-3 py-2 border border-gray-600 rounded-md shadow-sm bg-gray-700 text-gray-300 sm:text-sm"
                          disabled
                        />
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <p className="text-gray-400 text-sm">{t('sensors.readingsUnavailable')}</p>
                    </div>
                  )}

                  {/* Readings chart placeholder */}
                  <div className="mt-6 bg-gray-700 rounded-lg p-4 border border-gray-600 h-48 flex items-center justify-center">
                    <div className="text-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                      <p className="text-gray-400 text-sm">{t('sensors.historicalReadingsUnavailable')}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </form>
          </div>

          {/* Fixed footer with buttons */}
          <div className="border-t border-gray-700 p-3 bg-gray-900">
            <div className="flex flex-row space-x-3 justify-end max-w-3xl mx-auto">
              <button
                type="button"
                onClick={onCancel}
                className="px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-white bg-gray-800 hover:bg-gray-700"
              >
                {t('common.cancel')}
              </button>
              <button
                type="submit"
                form="sensor-form"
                disabled={isSubmitting || createSensor.isPending || updateSensor.isPending}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed flex items-center"
              >
                {(isSubmitting || createSensor.isPending || updateSensor.isPending) && (
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                )}
                {sensor ? t('sensors.updateSensor') : t('sensors.createSensor')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EditSensorForm;