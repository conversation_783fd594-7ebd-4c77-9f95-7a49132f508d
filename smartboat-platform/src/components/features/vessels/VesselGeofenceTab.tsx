import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../../context/AuthContext';
import { LoadingOverlay, StatusBadge, Pagination } from '../../common';
import wlpTemplateService from '../../../services/wlpTemplateService';
import geofenceAnalysisService from '../../../services/geofenceAnalysisService';
import { useVesselFenceEvents } from '../../../hooks/queries/useGeoFencingQueries';
import geoFencingService from '../../../services/geoFencingService';

interface VesselGeofenceTabProps {
  vesselId: string;
  vesselName?: string;
}

interface WlpTemplate {
  id: string;
  name: string;
  description?: string;
  zoneCount: number;
  zoneNames: string[];
  created: string;
}

interface AnalysisJob {
  id: string;
  vesselName: string;
  templateNames: string[];
  analysisDateFrom: string;
  analysisDateTo: string;
  jobStatus: string;
  coordinatesProcessed: number;
  fenceEventsCreated: number;
  errorMessage?: string;
  created: string;
}

interface FenceEvent {
  id: string;
  vesselId: string;
  exitTimestamp: string;
  exitLat: number;
  exitLng: number;
  entryTimestamp?: string;
  entryLat?: number;
  entryLng?: number;
  durationMinutes?: number;
  status: 'Active' | 'Completed';
  created: string;
  changed?: string;
  vesselName?: string;
  analysisJobId?: string;
  templateName?: string;
  zoneName?: string;
}

const VesselGeofenceTab: React.FC<VesselGeofenceTabProps> = ({
  vesselId,
  vesselName
}) => {
  const { t } = useTranslation();
  const { currentUser } = useAuth();
  const queryClient = useQueryClient();

  // Form state for analysis
  const [selectedTemplateIds, setSelectedTemplateIds] = useState<string[]>([]);
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');
  const [analysisName, setAnalysisName] = useState('');

  // UI state
  const [showAnalysisForm, setShowAnalysisForm] = useState(false);
  const [analysisError, setAnalysisError] = useState<string | null>(null);
  const [analysisSuccess, setAnalysisSuccess] = useState<string | null>(null);

  // Events filter state
  const [currentEventsPage, setCurrentEventsPage] = useState(1);
  const [eventsStatusFilter, setEventsStatusFilter] = useState<string>('');
  const [eventsTypeFilter, setEventsTypeFilter] = useState<string>(''); // All, System, Analysis
  const [eventsTemplateFilter, setEventsTemplateFilter] = useState<string>('');
  const eventsPageSize = 10;

  // Set default date range (last 30 days)
  useEffect(() => {
    const today = new Date();
    const thirtyDaysAgo = new Date(today);
    thirtyDaysAgo.setDate(today.getDate() - 30);

    setDateFrom(thirtyDaysAgo.toISOString().split('T')[0]);
    setDateTo(today.toISOString().split('T')[0]);
  }, []);

  // Fetch WLP templates
  const { data: templates, isLoading: templatesLoading } = useQuery({
    queryKey: ['wlpTemplates'],
    queryFn: () => wlpTemplateService.getTemplates({}),
    enabled: !!currentUser?.id
  });

  // Fetch vessel-specific analysis jobs
  const { data: analysisJobs, isLoading: jobsLoading } = useQuery({
    queryKey: ['geofenceAnalysisJobs', vesselId],
    queryFn: () => geofenceAnalysisService.getAnalysisJobs({ vesselId }),
    enabled: !!currentUser?.id && !!vesselId
  });

  // Fetch fence events with enhanced filtering
  const { 
    data: events = [], 
    isLoading: eventsLoading, 
    isError: eventsError, 
    error: eventsErrorDetails
  } = useVesselFenceEvents(vesselId, {
    status: eventsStatusFilter || undefined,
    pageLimit: eventsPageSize,
    pageOffset: (currentEventsPage - 1) * eventsPageSize,
  });

  // Create analysis job mutation
  const createAnalysisJobMutation = useMutation({
    mutationFn: (jobData: any) => geofenceAnalysisService.createAnalysisJob(jobData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['geofenceAnalysisJobs', vesselId] });
      queryClient.invalidateQueries({ queryKey: ['vesselFenceEvents', vesselId] });
      setAnalysisSuccess(t('geofenceAnalysis.createSuccess'));
      setShowAnalysisForm(false);
      resetForm();
      setTimeout(() => setAnalysisSuccess(null), 5000);
    },
    onError: (error: any) => {
      setAnalysisError(error.message || t('geofenceAnalysis.createError'));
      setTimeout(() => setAnalysisError(null), 5000);
    }
  });

  const resetForm = () => {
    setSelectedTemplateIds([]);
    setAnalysisName('');
    const today = new Date();
    const thirtyDaysAgo = new Date(today);
    thirtyDaysAgo.setDate(today.getDate() - 30);
    setDateFrom(thirtyDaysAgo.toISOString().split('T')[0]);
    setDateTo(today.toISOString().split('T')[0]);
  };

  const handleTemplateToggle = (templateId: string) => {
    setSelectedTemplateIds(prev => 
      prev.includes(templateId) 
        ? prev.filter(id => id !== templateId)
        : [...prev, templateId]
    );
  };

  const handleSelectAllTemplates = () => {
    if (selectedTemplateIds.length === templates?.length) {
      setSelectedTemplateIds([]);
    } else {
      setSelectedTemplateIds(templates?.map(t => t.id) || []);
    }
  };

  const handleDatePreset = (days: number) => {
    const today = new Date();
    const pastDate = new Date(today);
    pastDate.setDate(today.getDate() - days);
    
    setDateFrom(pastDate.toISOString().split('T')[0]);
    setDateTo(today.toISOString().split('T')[0]);
  };

  const handleCreateAnalysis = () => {
    // Validation
    if (selectedTemplateIds.length === 0) {
      setAnalysisError(t('geofenceAnalysis.templatesRequired'));
      setTimeout(() => setAnalysisError(null), 5000);
      return;
    }

    if (!dateFrom || !dateTo) {
      setAnalysisError(t('geofenceAnalysis.datesRequired'));
      setTimeout(() => setAnalysisError(null), 5000);
      return;
    }

    if (new Date(dateFrom) > new Date(dateTo)) {
      setAnalysisError(t('geofenceAnalysis.invalidDateRange'));
      setTimeout(() => setAnalysisError(null), 5000);
      return;
    }

    const jobData = {
      vesselId: vesselId,
      templateIds: selectedTemplateIds,
      analysisDateFrom: dateFrom + 'T00:00:00.000Z',
      analysisDateTo: dateTo + 'T23:59:59.999Z',
      name: analysisName || `Analysis for ${vesselName} - ${new Date().toLocaleDateString()}`
    };

    createAnalysisJobMutation.mutate(jobData);
  };

  const getJobStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed': return 'green';
      case 'running': return 'blue';
      case 'pending': return 'yellow';
      case 'failed': return 'red';
      default: return 'gray';
    }
  };

  const formatDateTime = (dateString: string): string => {
    return new Date(dateString).toLocaleString();
  };

  const formatCoordinates = (lat?: number, lng?: number) => {
    if (lat == null || lng == null) return 'N/A';
    return `${lat.toFixed(4)}, ${lng.toFixed(4)}`;
  };

  const getEventStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800';
      case 'Completed':
        return 'text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200 dark:text-gray-400 dark:bg-gray-900/20 dark:border-gray-800';
    }
  };

  // Filter events based on selected filters
  const filteredEvents = events.filter(event => {
    if (eventsTypeFilter === 'Analysis' && !event.analysisJobId) return false;
    if (eventsTypeFilter === 'System' && event.analysisJobId) return false;
    if (eventsTemplateFilter && event.templateName !== eventsTemplateFilter) return false;
    return true;
  });

  const uniqueTemplateNames = Array.from(new Set(events.map(e => e.templateName).filter(Boolean)));

  if (templatesLoading) {
    return <LoadingOverlay message={t('common.loading')} />;
  }

  return (
    <div className="space-y-6">
      {/* Status Messages */}
      {analysisError && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800 dark:text-red-200">{analysisError}</p>
            </div>
          </div>
        </div>
      )}

      {analysisSuccess && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-800 dark:text-green-200">{analysisSuccess}</p>
            </div>
          </div>
        </div>
      )}

      {/* Template Selection Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              {t('geofenceAnalysis.selectTemplates')}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Choose WLP templates to analyze vessel coordinates against
            </p>
          </div>
          <button
            onClick={() => setShowAnalysisForm(!showAnalysisForm)}
            className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
          >
            {showAnalysisForm ? t('common.cancel') : t('geofenceAnalysis.newAnalysis')}
          </button>
        </div>

        {showAnalysisForm && (
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="space-y-6">
              {/* Analysis Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('geofenceAnalysis.analysisName')}
                </label>
                <input
                  type="text"
                  value={analysisName}
                  onChange={(e) => setAnalysisName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder={t('geofenceAnalysis.analysisNamePlaceholder')}
                />
              </div>

              {/* Date Range */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Analysis Date Range *
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                  <div>
                    <input
                      type="date"
                      value={dateFrom}
                      onChange={(e) => setDateFrom(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <div>
                    <input
                      type="date"
                      value={dateTo}
                      onChange={(e) => setDateTo(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>
                {/* Quick preset buttons */}
                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={() => handleDatePreset(7)}
                    className="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
                  >
                    Last 7 days
                  </button>
                  <button
                    onClick={() => handleDatePreset(30)}
                    className="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
                  >
                    Last 30 days
                  </button>
                  <button
                    onClick={() => handleDatePreset(90)}
                    className="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
                  >
                    Last 90 days
                  </button>
                </div>
              </div>

              {/* Template Selection */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Select Templates * ({selectedTemplateIds.length} selected)
                  </label>
                  {templates && templates.length > 0 && (
                    <button
                      onClick={handleSelectAllTemplates}
                      className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                    >
                      {selectedTemplateIds.length === templates.length ? 'Deselect All' : 'Select All'}
                    </button>
                  )}
                </div>
                
                {templates?.length === 0 ? (
                  <div className="text-sm text-gray-500 dark:text-gray-400 p-4 border border-gray-200 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700">
                    <p className="mb-2">{t('geofenceAnalysis.noTemplatesAvailable')}</p>
                    <p className="text-xs">Upload WLP templates in your profile to enable geofence analysis.</p>
                  </div>
                ) : (
                  <div className="space-y-2 max-h-60 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-md p-3">
                    {templates?.map((template: WlpTemplate) => (
                      <label key={template.id} className="flex items-start space-x-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 p-2 rounded">
                        <input
                          type="checkbox"
                          checked={selectedTemplateIds.includes(template.id)}
                          onChange={() => handleTemplateToggle(template.id)}
                          className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                        />
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {template.name}
                          </div>
                          {template.description && (
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {template.description}
                            </div>
                          )}
                          <div className="text-xs text-gray-400 dark:text-gray-500">
                            {template.zoneCount} zones • {template.zoneNames.slice(0, 3).join(', ')}
                            {template.zoneNames.length > 3 && '...'}
                          </div>
                        </div>
                      </label>
                    ))}
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex items-center justify-end space-x-3 pt-4">
                <button
                  onClick={() => setShowAnalysisForm(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600"
                >
                  {t('common.cancel')}
                </button>
                <button
                  onClick={handleCreateAnalysis}
                  disabled={createAnalysisJobMutation.isPending || selectedTemplateIds.length === 0}
                  className="px-6 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {createAnalysisJobMutation.isPending ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      {t('geofenceAnalysis.creatingAnalysis')}
                    </div>
                  ) : (
                    'Analyze Selected Period'
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Analysis Job History */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Analysis Job History
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Track analysis runs for this vessel
          </p>
        </div>

        {jobsLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <span className="ml-3 text-gray-600 dark:text-gray-400">
              {t('common.loading')}
            </span>
          </div>
        ) : analysisJobs?.length === 0 ? (
          <div className="text-center py-12">
            <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
              <path d="M34 40h10v-4a6 6 0 00-10.712-3.714M34 40H14m20 0v-4a9.971 9.971 0 00-.712-3.714M14 40H4v-4a6 6 0 0110.713-3.714M14 40v-4c0-1.313.253-2.566.713-3.714m0 0A10.003 10.003 0 0124 26c4.21 0 7.813 2.602 9.288 6.286" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
              No analysis jobs yet
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Select templates and date range above to run your first analysis
            </p>
          </div>
        ) : (
          <div className="overflow-hidden">
            <table className="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-900">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Templates Used
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Date Range
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Results
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Analysis Date
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {analysisJobs?.map((job: AnalysisJob) => (
                  <tr key={job.id} className="hover:bg-gray-50 dark:hover:bg-gray-750">
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {job.templateNames.join(', ')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {new Date(job.analysisDateFrom).toLocaleDateString()}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        to {new Date(job.analysisDateTo).toLocaleDateString()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <StatusBadge
                        status={job.jobStatus}
                        colorScheme={getJobStatusColor(job.jobStatus)}
                      />
                      {job.errorMessage && (
                        <div className="text-xs text-red-600 dark:text-red-400 mt-1">
                          {job.errorMessage}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {job.coordinatesProcessed.toLocaleString()} coordinates
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {job.fenceEventsCreated} boundary crossings
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {formatDateTime(job.created)}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Enhanced Boundary Crossing Events */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Boundary Crossing Events
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                Track when this vessel crosses geofence boundaries
              </p>
            </div>
            
            {/* Enhanced Filters */}
            <div className="flex items-center space-x-3">
              <select
                value={eventsTypeFilter}
                onChange={(e) => setEventsTypeFilter(e.target.value)}
                className="px-3 py-1.5 text-sm border border-gray-300 rounded-md bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Events</option>
                <option value="System">System Events</option>
                <option value="Analysis">Analysis Events</option>
              </select>
              
              {uniqueTemplateNames.length > 0 && (
                <select
                  value={eventsTemplateFilter}
                  onChange={(e) => setEventsTemplateFilter(e.target.value)}
                  className="px-3 py-1.5 text-sm border border-gray-300 rounded-md bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Templates</option>
                  {uniqueTemplateNames.map(name => (
                    <option key={name} value={name}>{name}</option>
                  ))}
                </select>
              )}
              
              <select
                value={eventsStatusFilter}
                onChange={(e) => setEventsStatusFilter(e.target.value)}
                className="px-3 py-1.5 text-sm border border-gray-300 rounded-md bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Status</option>
                <option value="Active">Currently Outside</option>
                <option value="Completed">Returned to Fence</option>
              </select>
            </div>
          </div>
        </div>

        <div className="p-6">
          {eventsLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
              <span className="ml-3 text-gray-600 dark:text-gray-400">
                {t('common.loading')}
              </span>
            </div>
          ) : eventsError ? (
            <div className="bg-red-50 border border-red-200 rounded-lg p-6 dark:bg-red-900/20 dark:border-red-800">
              <h3 className="text-lg font-medium text-red-800 dark:text-red-200 mb-2">
                Error Loading Events
              </h3>
              <p className="text-red-700 dark:text-red-300">
                Failed to load boundary crossing events. Please try again later.
              </p>
            </div>
          ) : filteredEvents.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 mx-auto mb-4 text-gray-400">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                </svg>
              </div>
              <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                No Boundary Crossings
              </h4>
              <p className="text-gray-500 dark:text-gray-400 mt-2">
                {eventsTypeFilter || eventsTemplateFilter || eventsStatusFilter
                  ? 'No events found with the selected filters.'
                  : 'This vessel has not crossed any geofence boundaries yet.'
                }
              </p>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-900">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Exit Time
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Exit Coordinates
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Zone/Template
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Duration
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Return Time
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Source
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {filteredEvents.map((event: FenceEvent) => (
                      <tr key={event.id} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                        <td className="px-4 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getEventStatusColor(event.status)}`}>
                            {geoFencingService.formatStatus(event.status)}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {formatDateTime(event.exitTimestamp)}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 font-mono">
                          {formatCoordinates(event.exitLat, event.exitLng)}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 dark:text-gray-100">
                            {event.zoneName || 'N/A'}
                          </div>
                          {event.templateName && (
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {event.templateName}
                            </div>
                          )}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {geoFencingService.formatDuration(event.durationMinutes)}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {event.entryTimestamp ? formatDateTime(event.entryTimestamp) : 
                           <span className="text-orange-600 dark:text-orange-400">Still outside</span>
                          }
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            event.analysisJobId 
                              ? 'text-blue-700 bg-blue-100 dark:text-blue-300 dark:bg-blue-900/30' 
                              : 'text-gray-700 bg-gray-100 dark:text-gray-300 dark:bg-gray-700'
                          }`}>
                            {event.analysisJobId ? 'Analysis' : 'System'}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {events.length > eventsPageSize && (
                <div className="mt-6">
                  <Pagination
                    currentPage={currentEventsPage}
                    totalPages={Math.ceil(filteredEvents.length / eventsPageSize)}
                    onPageChange={setCurrentEventsPage}
                    showInfo={true}
                    itemsPerPage={eventsPageSize}
                    totalItems={filteredEvents.length}
                  />
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default VesselGeofenceTab;