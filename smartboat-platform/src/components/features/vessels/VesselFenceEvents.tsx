import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useVesselFenceEvents } from '../../../hooks/queries/useGeoFencingQueries';
import geoFencingService from '../../../services/geoFencingService';
import { Pagination } from '../../common';

interface VesselFenceEventsProps {
  vesselId: string;
  className?: string;
}

interface FenceEvent {
  id: string;
  vesselId: string;
  exitTimestamp: string;
  exitLat: number;
  exitLng: number;
  entryTimestamp?: string;
  entryLat?: number;
  entryLng?: number;
  durationMinutes?: number;
  status: 'Active' | 'Completed';
  created: string;
  changed?: string;
  vesselName?: string;
}

const VesselFenceEvents: React.FC<VesselFenceEventsProps> = ({ 
  vesselId, 
  className = '' 
}) => {
  const { t } = useTranslation();
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const pageSize = 10;

  const { 
    data: events = [], 
    isLoading, 
    isError, 
    error 
  } = useVesselFenceEvents(vesselId, {
    status: statusFilter || undefined,
    pageLimit: pageSize,
    pageOffset: (currentPage - 1) * pageSize,
  });

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleStatusFilterChange = (status: string) => {
    setStatusFilter(status);
    setCurrentPage(1); // Reset to first page when filter changes
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const formatCoordinates = (lat?: number, lng?: number) => {
    if (lat == null || lng == null) return 'N/A';
    return `${lat.toFixed(4)}, ${lng.toFixed(4)}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800';
      case 'Completed':
        return 'text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200 dark:text-gray-400 dark:bg-gray-900/20 dark:border-gray-800';
    }
  };

  if (isError) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-6 dark:bg-red-900/20 dark:border-red-800 ${className}`}>
        <h3 className="text-lg font-medium text-red-800 dark:text-red-200 mb-2">
          {t('geoFencing.error.title', 'Error Loading Fence Events')}
        </h3>
        <p className="text-red-700 dark:text-red-300">
          {t('geoFencing.error.message', 'Failed to load vessel fence events. Please try again later.')}
        </p>
        {error && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-2">
            {error.message}
          </p>
        )}
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              {t('geoFencing.title', 'Geo-fencing Events')}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {t('geoFencing.description', 'Track when this vessel exits and returns to the geo-fence')}
            </p>
          </div>
          
          {/* Status Filter */}
          <div className="flex items-center space-x-2">
            <label htmlFor="status-filter" className="text-sm text-gray-700 dark:text-gray-300">
              {t('geoFencing.filter.status', 'Status:')}
            </label>
            <select
              id="status-filter"
              value={statusFilter}
              onChange={(e) => handleStatusFilterChange(e.target.value)}
              className="px-3 py-1.5 text-sm border border-gray-300 rounded-md bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">{t('geoFencing.filter.all', 'All Events')}</option>
              <option value="Active">{t('geoFencing.status.active', 'Currently Outside')}</option>
              <option value="Completed">{t('geoFencing.status.completed', 'Returned to Fence')}</option>
            </select>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
            <span className="ml-3 text-gray-600 dark:text-gray-400">
              {t('common.loading', 'Loading...')}
            </span>
          </div>
        ) : events.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 mx-auto mb-4 text-gray-400">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
              </svg>
            </div>
            <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              {t('geoFencing.empty.title', 'No Fence Events')}
            </h4>
            <p className="text-gray-500 dark:text-gray-400 mt-2">
              {statusFilter 
                ? t('geoFencing.empty.filtered', 'No events found with the selected filter.')
                : t('geoFencing.empty.message', 'This vessel has not exited the geo-fence yet.')
              }
            </p>
          </div>
        ) : (
          <>
            {/* Events Table */}
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-900">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      {t('geoFencing.table.status', 'Status')}
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      {t('geoFencing.table.exitTime', 'Exit Time')}
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      {t('geoFencing.table.exitCoords', 'Exit Coordinates')}
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      {t('geoFencing.table.duration', 'Duration')}
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      {t('geoFencing.table.entryTime', 'Return Time')}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {events.map((event: FenceEvent) => (
                    <tr key={event.id} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                      <td className="px-4 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(event.status)}`}>
                          {geoFencingService.formatStatus(event.status)}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {formatDateTime(event.exitTimestamp)}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 font-mono">
                        {formatCoordinates(event.exitLat, event.exitLng)}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {geoFencingService.formatDuration(event.durationMinutes)}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {event.entryTimestamp ? formatDateTime(event.entryTimestamp) : 
                         <span className="text-orange-600 dark:text-orange-400">{t('geoFencing.status.ongoing', 'Still outside')}</span>
                        }
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="mt-6">
              <Pagination
                currentPage={currentPage}
                totalPages={Math.ceil(events.length / pageSize)}
                onPageChange={handlePageChange}
                showInfo={true}
                itemsPerPage={pageSize}
                totalItems={events.length}
              />
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default VesselFenceEvents;