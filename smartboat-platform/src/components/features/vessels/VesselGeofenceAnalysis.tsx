import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../../context/AuthContext';
import { LoadingOverlay, StatusBadge } from '../../common';
import vesselService from '../../../services/vesselService';
import wlpTemplateService from '../../../services/wlpTemplateService';
import geofenceAnalysisService from '../../../services/geofenceAnalysisService';

interface VesselGeofenceAnalysisProps {
  preSelectedVesselId?: string;
  onClose?: () => void;
}

interface Vessel {
  id: string;
  name: string;
  imo?: string;
  mmsi?: string;
  company?: string;
  status: string;
  lastLocation?: {
    latitude: number;
    longitude: number;
  };
}

interface WlpTemplate {
  id: string;
  name: string;
  description?: string;
  zoneCount: number;
  zoneNames: string[];
  created: string;
}

interface AnalysisJob {
  id: string;
  vesselName: string;
  templateNames: string[];
  analysisDateFrom: string;
  analysisDateTo: string;
  jobStatus: string;
  coordinatesProcessed: number;
  fenceEventsCreated: number;
  errorMessage?: string;
  created: string;
}

const VesselGeofenceAnalysis: React.FC<VesselGeofenceAnalysisProps> = ({
  preSelectedVesselId,
  onClose
}) => {
  const { t } = useTranslation();
  const { currentUser } = useAuth();
  const queryClient = useQueryClient();

  // Form state
  const [selectedVesselId, setSelectedVesselId] = useState(preSelectedVesselId || '');
  const [selectedTemplateIds, setSelectedTemplateIds] = useState<string[]>([]);
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');
  const [analysisName, setAnalysisName] = useState('');

  // UI state
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [analysisError, setAnalysisError] = useState<string | null>(null);
  const [analysisSuccess, setAnalysisSuccess] = useState<string | null>(null);

  // Set default date range (last 30 days)
  useEffect(() => {
    const today = new Date();
    const thirtyDaysAgo = new Date(today);
    thirtyDaysAgo.setDate(today.getDate() - 30);

    setDateFrom(thirtyDaysAgo.toISOString().split('T')[0]);
    setDateTo(today.toISOString().split('T')[0]);
  }, []);

  // Fetch vessels
  const { data: vessels, isLoading: vesselsLoading } = useQuery({
    queryKey: ['vessels'],
    queryFn: () => vesselService.getVessels({}),
    enabled: !!currentUser?.id
  });

  // Fetch WLP templates
  const { data: templates, isLoading: templatesLoading } = useQuery({
    queryKey: ['wlpTemplates'],
    queryFn: () => wlpTemplateService.getTemplates({}),
    enabled: !!currentUser?.id && currentUser.role === 'Administrator'
  });

  // Fetch analysis jobs
  const { data: analysisJobs, isLoading: jobsLoading } = useQuery({
    queryKey: ['geofenceAnalysisJobs'],
    queryFn: () => geofenceAnalysisService.getAnalysisJobs({}),
    enabled: !!currentUser?.id
  });

  // Create analysis job mutation
  const createAnalysisJobMutation = useMutation({
    mutationFn: (jobData: any) => geofenceAnalysisService.createAnalysisJob(jobData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['geofenceAnalysisJobs'] });
      setAnalysisSuccess(t('geofenceAnalysis.createSuccess'));
      setShowCreateForm(false);
      resetForm();
      setTimeout(() => setAnalysisSuccess(null), 5000);
    },
    onError: (error: any) => {
      setAnalysisError(error.message || t('geofenceAnalysis.createError'));
      setTimeout(() => setAnalysisError(null), 5000);
    }
  });

  const resetForm = () => {
    if (!preSelectedVesselId) {
      setSelectedVesselId('');
    }
    setSelectedTemplateIds([]);
    setAnalysisName('');
    const today = new Date();
    const thirtyDaysAgo = new Date(today);
    thirtyDaysAgo.setDate(today.getDate() - 30);
    setDateFrom(thirtyDaysAgo.toISOString().split('T')[0]);
    setDateTo(today.toISOString().split('T')[0]);
  };

  const handleTemplateToggle = (templateId: string) => {
    setSelectedTemplateIds(prev => 
      prev.includes(templateId) 
        ? prev.filter(id => id !== templateId)
        : [...prev, templateId]
    );
  };

  const handleCreateAnalysis = () => {
    // Validation
    if (!selectedVesselId) {
      setAnalysisError(t('geofenceAnalysis.vesselRequired'));
      setTimeout(() => setAnalysisError(null), 5000);
      return;
    }

    if (selectedTemplateIds.length === 0) {
      setAnalysisError(t('geofenceAnalysis.templatesRequired'));
      setTimeout(() => setAnalysisError(null), 5000);
      return;
    }

    if (!dateFrom || !dateTo) {
      setAnalysisError(t('geofenceAnalysis.datesRequired'));
      setTimeout(() => setAnalysisError(null), 5000);
      return;
    }

    if (new Date(dateFrom) > new Date(dateTo)) {
      setAnalysisError(t('geofenceAnalysis.invalidDateRange'));
      setTimeout(() => setAnalysisError(null), 5000);
      return;
    }

    const jobData = {
      vesselId: selectedVesselId,
      templateIds: selectedTemplateIds,
      analysisDateFrom: dateFrom + 'T00:00:00.000Z',
      analysisDateTo: dateTo + 'T23:59:59.999Z',
      name: analysisName || `Analysis for ${vessels?.find(v => v.id === selectedVesselId)?.name} - ${new Date().toLocaleDateString()}`
    };

    createAnalysisJobMutation.mutate(jobData);
  };

  const getJobStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed': return 'green';
      case 'running': return 'blue';
      case 'pending': return 'yellow';
      case 'failed': return 'red';
      default: return 'gray';
    }
  };

  const formatDateTime = (dateString: string): string => {
    return new Date(dateString).toLocaleString();
  };

  if (vesselsLoading || templatesLoading) {
    return <LoadingOverlay message={t('common.loading')} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            {t('geofenceAnalysis.title')}
          </h2>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {t('geofenceAnalysis.description')}
          </p>
        </div>
        <div className="flex items-center space-x-3">
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
          <button
            onClick={() => setShowCreateForm(!showCreateForm)}
            className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
          >
            {showCreateForm ? t('common.cancel') : t('geofenceAnalysis.newAnalysis')}
          </button>
        </div>
      </div>

      {/* Status Messages */}
      {analysisError && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800 dark:text-red-200">{analysisError}</p>
            </div>
          </div>
        </div>
      )}

      {analysisSuccess && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-800 dark:text-green-200">{analysisSuccess}</p>
            </div>
          </div>
        </div>
      )}

      {/* Create Analysis Form */}
      {showCreateForm && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-6">
            {t('geofenceAnalysis.newAnalysis')}
          </h3>

          <div className="space-y-6">
            {/* Analysis Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('geofenceAnalysis.analysisName')}
              </label>
              <input
                type="text"
                value={analysisName}
                onChange={(e) => setAnalysisName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder={t('geofenceAnalysis.analysisNamePlaceholder')}
              />
            </div>

            {/* Vessel Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('geofenceAnalysis.selectVessel')} *
              </label>
              <select
                value={selectedVesselId}
                onChange={(e) => setSelectedVesselId(e.target.value)}
                disabled={!!preSelectedVesselId}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <option value="">{t('geofenceAnalysis.chooseVessel')}</option>
                {vessels?.map((vessel: Vessel) => (
                  <option key={vessel.id} value={vessel.id}>
                    {vessel.name} {vessel.imo && `(IMO: ${vessel.imo})`}
                  </option>
                ))}
              </select>
            </div>

            {/* Date Range */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('geofenceAnalysis.dateFrom')} *
                </label>
                <input
                  type="date"
                  value={dateFrom}
                  onChange={(e) => setDateFrom(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('geofenceAnalysis.dateTo')} *
                </label>
                <input
                  type="date"
                  value={dateTo}
                  onChange={(e) => setDateTo(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            {/* Template Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('geofenceAnalysis.selectTemplates')} *
              </label>
              {templates?.length === 0 ? (
                <div className="text-sm text-gray-500 dark:text-gray-400 p-4 border border-gray-200 dark:border-gray-600 rounded-md">
                  {t('geofenceAnalysis.noTemplatesAvailable')}
                </div>
              ) : (
                <div className="space-y-2 max-h-60 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-md p-3">
                  {templates?.map((template: WlpTemplate) => (
                    <label key={template.id} className="flex items-start space-x-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750 p-2 rounded">
                      <input
                        type="checkbox"
                        checked={selectedTemplateIds.includes(template.id)}
                        onChange={() => handleTemplateToggle(template.id)}
                        className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                      />
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {template.name}
                        </div>
                        {template.description && (
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {template.description}
                          </div>
                        )}
                        <div className="text-xs text-gray-400 dark:text-gray-500">
                          {template.zoneCount} {t('wlpTemplates.zones')} • {template.zoneNames.slice(0, 3).join(', ')}
                          {template.zoneNames.length > 3 && '...'}
                        </div>
                      </div>
                    </label>
                  ))}
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={() => setShowCreateForm(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600"
              >
                {t('common.cancel')}
              </button>
              <button
                onClick={handleCreateAnalysis}
                disabled={createAnalysisJobMutation.isPending}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {createAnalysisJobMutation.isPending ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    {t('geofenceAnalysis.creatingAnalysis')}
                  </div>
                ) : (
                  t('geofenceAnalysis.createAnalysis')
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Analysis Jobs List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {t('geofenceAnalysis.analysisJobs')}
          </h3>
        </div>

        {jobsLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <span className="ml-3 text-gray-600 dark:text-gray-400">
              {t('common.loading')}
            </span>
          </div>
        ) : analysisJobs?.length === 0 ? (
          <div className="text-center py-12">
            <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
              <path d="M34 40h10v-4a6 6 0 00-10.712-3.714M34 40H14m20 0v-4a9.971 9.971 0 00-.712-3.714M14 40H4v-4a6 6 0 0110.713-3.714M14 40v-4c0-1.313.253-2.566.713-3.714m0 0A10.003 10.003 0 0124 26c4.21 0 7.813 2.602 9.288 6.286" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
              {t('geofenceAnalysis.noAnalysisJobs')}
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {t('geofenceAnalysis.noAnalysisJobsDescription')}
            </p>
          </div>
        ) : (
          <div className="overflow-hidden">
            <table className="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-900">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t('geofenceAnalysis.vessel')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t('geofenceAnalysis.templates')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t('geofenceAnalysis.dateRange')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t('geofenceAnalysis.status')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t('geofenceAnalysis.results')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t('geofenceAnalysis.created')}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {analysisJobs?.map((job: AnalysisJob) => (
                  <tr key={job.id} className="hover:bg-gray-50 dark:hover:bg-gray-750">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {job.vesselName}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {job.templateNames.join(', ')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {new Date(job.analysisDateFrom).toLocaleDateString()}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {t('geofenceAnalysis.to')} {new Date(job.analysisDateTo).toLocaleDateString()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <StatusBadge
                        status={job.jobStatus}
                        colorScheme={getJobStatusColor(job.jobStatus)}
                      />
                      {job.errorMessage && (
                        <div className="text-xs text-red-600 dark:text-red-400 mt-1">
                          {job.errorMessage}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {job.coordinatesProcessed.toLocaleString()} {t('geofenceAnalysis.coordinates')}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {job.fenceEventsCreated} {t('geofenceAnalysis.events')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {formatDateTime(job.created)}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default VesselGeofenceAnalysis;