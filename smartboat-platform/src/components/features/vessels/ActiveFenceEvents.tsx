import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useActiveFenceEvents } from '../../../hooks/queries/useGeoFencingQueries';
import { LoadingOverlay, ErrorNotification, StatusBadge } from '../../common';
import { formatDuration, formatCoordinates } from '../../../utils/geoFencingUtils';

const ActiveFenceEvents: React.FC = () => {
  const { t } = useTranslation();
  const { 
    data: activeEvents, 
    isLoading, 
    error, 
    refetch 
  } = useActiveFenceEvents();

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <LoadingOverlay 
          message={t('geoFencing.loadingActiveEvents', 'Loading active fence events...')}
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <ErrorNotification
          message={t('geoFencing.errorLoadingActiveEvents', 'Failed to load active fence events')}
          error={error}
          onRetry={refetch}
        />
      </div>
    );
  }

  const events = activeEvents?.data || [];

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md">
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {t('geoFencing.vesselsOutsideFence', 'Vessels Outside Geo-fence')}
          </h3>
          <div className="flex items-center space-x-2">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse mr-2"></div>
              <span className="text-sm text-gray-600 dark:text-gray-300">
                {events.length} {t('geoFencing.activeEvents', 'active')}
              </span>
            </div>
            <button
              onClick={refetch}
              className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
              title={t('common.refresh', 'Refresh')}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      <div className="p-6">
        {events.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-green-500 mb-2">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {t('geoFencing.allVesselsInside', 'All Vessels Inside Fence')}
            </h4>
            <p className="text-gray-500 dark:text-gray-400">
              {t('geoFencing.noActiveViolations', 'No vessels are currently outside the geo-fence boundary.')}
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {events.map((event) => (
              <div
                key={event.id}
                className="border border-red-200 dark:border-red-800 rounded-lg p-4 bg-red-50 dark:bg-red-900/20"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {event.vessel?.name || t('geoFencing.unknownVessel', 'Unknown Vessel')}
                      </h4>
                      <StatusBadge status="warning" />
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">
                          {t('geoFencing.exitTime', 'Exit Time')}:
                        </span>
                        <span className="ml-2 text-gray-900 dark:text-white">
                          {new Date(event.exitTimestamp).toLocaleString()}
                        </span>
                      </div>
                      
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">
                          {t('geoFencing.duration', 'Duration')}:
                        </span>
                        <span className="ml-2 text-gray-900 dark:text-white">
                          {formatDuration(event.exitTimestamp)}
                        </span>
                      </div>
                      
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">
                          {t('geoFencing.exitLocation', 'Exit Location')}:
                        </span>
                        <span className="ml-2 text-gray-900 dark:text-white">
                          {formatCoordinates(event.exitLat, event.exitLng)}
                        </span>
                      </div>
                      
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">
                          {t('common.status', 'Status')}:
                        </span>
                        <span className="ml-2 text-red-600 dark:text-red-400 font-medium">
                          {t('geoFencing.outsideFence', 'Outside Fence')}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <Link
                      to={`/vessels/${event.vesselId}`}
                      className="inline-flex items-center px-3 py-1 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
                    >
                      {t('common.view', 'View')}
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ActiveFenceEvents;