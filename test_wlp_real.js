#!/usr/bin/env node

/**
 * Test script to verify WLP Template creation endpoint with real JWT token and .wlp file
 */

const https = require('https');
const fs = require('fs');
const crypto = require('crypto');

// Test configuration
const API_BASE_URL = 'https://localhost:7001';
const APPLICATION_ID = '03FC0B90-DFAD-11EE-8D86-0800200C9A66';

// Real JWT token provided by user
const REAL_JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLjbSgUD8t4OsO986AbYvl_OVJhsEaSkMOGUYzB3gic';

// Function to generate UUID v4
function uuidv4() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// Function to create request envelope
function createRequestEnvelope(payload, userId = '08ddcb9c-29ab-49d8-a7b9-faccf5caf282') {
  return {
    Header: {
      Id: uuidv4(),
      Application: APPLICATION_ID,
      Bank: "BANK",
      UserId: userId
    },
    Payload: payload
  };
}

// Function to make HTTPS request
function makeRequest(endpoint, data, token) {
  return new Promise((resolve, reject) => {
    const url = new URL(`${API_BASE_URL}${endpoint}`);

    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json'
      },
      // Ignore SSL certificate errors for localhost testing
      rejectUnauthorized: false
    };

    const req = https.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsedData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(JSON.stringify(data));
    req.end();
  });
}

// Main test function
async function testWlpEndpoint() {
  console.log('Testing WLP Template Creation Endpoint with Real Data...');

  try {
    // Read the real .wlp file
    const wlpFilePath = './SmartBoat.API/Implementations/GeoFencingService/geofenceData.wlp';

    if (!fs.existsSync(wlpFilePath)) {
      console.error('❌ WLP file not found:', wlpFilePath);
      return;
    }

    const wlpFileContent = fs.readFileSync(wlpFilePath, 'utf8');
    const base64Content = Buffer.from(wlpFileContent).toString('base64');

    console.log('✅ Successfully loaded WLP file');
    console.log('File size:', wlpFileContent.length, 'bytes');

    // Create the payload with a different name but same content (should trigger duplicate detection)
    const payload = {
      name: "Different Name Same Content Test", // Different name but same file content
      description: "Testing duplicate detection with different template name",
      fileName: "geofenceData.wlp",
      originalFilename: "geofenceData.wlp",
      fileContentBase64: base64Content
    };

    // Create the request envelope
    const requestData = createRequestEnvelope(payload);

    console.log('Request URL:', `${API_BASE_URL}/api/WlpTemplate/create`);
    console.log('Request Body Structure:');
    console.log(JSON.stringify({
      Header: requestData.Header,
      Payload: {
        name: payload.name,
        description: payload.description,
        fileName: payload.fileName,
        originalFilename: payload.originalFilename,
        fileContentBase64: `${base64Content.substring(0, 50)}...` // Show first 50 chars
      }
    }, null, 2));

    // Make the request
    const response = await makeRequest('/api/WlpTemplate/create', requestData, REAL_JWT_TOKEN);

    console.log('\n--- Response ---');
    console.log('Status Code:', response.statusCode);
    console.log('Headers:', response.headers);
    console.log('\nResponse Body:');
    console.log(JSON.stringify(response.data, null, 2));

    if (response.statusCode === 200) {
      console.log('\n✅ SUCCESS: WLP template created successfully!');
    } else if (response.statusCode === 400 && response.data.exception?.description?.includes('already exists')) {
      console.log('\n✅ SUCCESS: Request structure is correct (template already exists)');
    } else if (response.statusCode === 401) {
      console.log('\n❌ AUTHENTICATION ERROR: Invalid or expired JWT token');
    } else {
      console.log('\n❌ ERROR: Unexpected response');
    }

  } catch (error) {
    console.error('❌ ERROR:', error.message);
  }
}

// Run the test
testWlpEndpoint();
