**/.claude/settings.local.json

# Top-level ignores for the entire workspace

# OS/Editor
.DS_Store
.vscode/
.idea/
.vs/
*.suo
*.user
*.userosscache
*.sln.docstates

# Local env/config
.env
.env.*
*.local
*.local.*

# Node (root)
node_modules/

# Python (root helpers)
.venv/
__pycache__/

# .NET build/test artifacts (apply to all projects)
**/[Bb]in/
**/[Oo]bj/
**/[Oo]ut/
**/[Ll]og/
**/[Ll]ogs/
**/[Tt]est[Rr]esult*/
**/TestResults/
*.trx
*.coverage
*.coveragexml
coverage*.json
coverage*.xml
coverage*.info

# Tooling/benchmarks
BenchmarkDotNet.Artifacts/

# Misc
*.pidb
*.log
