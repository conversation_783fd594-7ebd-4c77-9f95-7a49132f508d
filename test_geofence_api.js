const axios = require('axios');
const https = require('https');

const API_BASE_URL = process.env.API_BASE_URL || 'https://localhost:7001';
const JWT_TOKEN = process.env.TOKEN || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.RyPSZU2y8ykey7iA7jEJo0WFuRbO_MMc9pnxnbj5ZdM';

// Configure axios (ignore self-signed certs for local https)
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    Authorization: `Bearer ${JWT_TOKEN}`,
    'Content-Type': 'application/json',
  },
  httpsAgent: new https.Agent({ rejectUnauthorized: false }),
});

async function testGeofenceAnalysisAPI() {
  console.log('🚀 Testing Geofence Analysis API endpoints...\n');

  try {
    // Test 1: Get analysis jobs list
    console.log('📋 Test 1: Get Analysis Jobs List');
    console.log('POST /api/geofence-analysis/jobs/list');
    
    const jobsResponse = await api.post('/api/geofence-analysis/jobs/list', {});
    console.log('✅ Status:', jobsResponse.status);
    console.log('📊 Response data:', JSON.stringify(jobsResponse.data, null, 2));
    console.log('📈 Jobs found:', Array.isArray(jobsResponse.data) ? jobsResponse.data.length : 'N/A');
    console.log();

  } catch (error) {
    console.log('❌ Error getting analysis jobs:');
    console.log('Status:', error.response?.status);
    console.log('Error:', error.response?.data || error.message);
    console.log();
  }

  try {
    // Test 2: Get WLP templates (needed for creating analysis jobs)
    console.log('📋 Test 2: Get WLP Templates');
    console.log('POST /api/WlpTemplate/list');

    const templatesResponse = await api.post('/api/WlpTemplate/list', { Header: {}, Payload: {} });
    const templatePayload = templatesResponse.data?.payload || templatesResponse.data?.Payload || [];

    console.log('✅ Status:', templatesResponse.status);
    console.log('📊 Templates found:', Array.isArray(templatePayload) ? templatePayload.length : 'N/A');

    if (Array.isArray(templatePayload) && templatePayload.length > 0) {
      console.log('🎯 First template:', {
        id: templatePayload[0].id,
        name: templatePayload[0].name,
        zoneCount: templatePayload[0].zoneCount,
      });
    }
    console.log();

  } catch (error) {
    console.log('❌ Error getting WLP templates:');
    console.log('Status:', error.response?.status);
    console.log('Error:', error.response?.data || error.message);
    console.log();
  }

  try {
    // Test 3: Get vessels (needed for creating analysis jobs)
    console.log('📋 Test 3: Get Vessels');
    console.log('POST /api/vessel/list');

    const vesselsResponse = await api.post('/api/vessel/list', { Header: {}, Payload: { pageLimit: 25, pageOffset: 0, sortField: 'name', sortOrder: 'ASC' } });
    const vesselsPayload = vesselsResponse.data?.payload || vesselsResponse.data?.Payload || {};
    const vessels = vesselsPayload?.data || vesselsPayload?.Data || [];

    console.log('✅ Status:', vesselsResponse.status);
    console.log('📊 Vessels found:', Array.isArray(vessels) ? vessels.length : 'N/A');

    if (Array.isArray(vessels) && vessels.length > 0) {
      console.log('🚢 First vessel:', {
        id: vessels[0].id,
        name: vessels[0].name,
        status: vessels[0].status,
      });
    }
    console.log();

  } catch (error) {
    console.log('❌ Error getting vessels:');
    console.log('Status:', error.response?.status);
    console.log('Error:', error.response?.data || error.message);
    console.log();
  }

  // Test 4: Create analysis job (if we have templates and vessels)
  try {
    console.log('📋 Test 4: Create Analysis Job');
    console.log('POST /api/geofence-analysis/jobs/create');

    // Get templates and vessels first
    const templatesResponse = await api.post('/api/WlpTemplate/list', { Header: {}, Payload: {} });
    const vesselsResponse = await api.post('/api/vessel/list', { Header: {}, Payload: { pageLimit: 25, pageOffset: 0, sortField: 'name', sortOrder: 'ASC' } });
    const templatePayload2 = templatesResponse.data?.payload || templatesResponse.data?.Payload || [];
    const vesselsPayload2 = vesselsResponse.data?.payload || vesselsResponse.data?.Payload || {};
    const vessels2 = vesselsPayload2?.data || vesselsPayload2?.Data || [];

    if (templatePayload2?.length > 0 && vessels2?.length > 0) {
      const testJobData = {
        vesselId: vessels2[0].id,
        templateIds: [templatePayload2[0].id],
        analysisDateFrom: '2024-01-01T00:00:00.000Z',
        analysisDateTo: '2024-01-31T23:59:59.999Z',
        name: 'API Test Analysis Job'
      };

      console.log('📤 Sending job data:', JSON.stringify(testJobData, null, 2));

      const createResponse = await api.post('/api/geofence-analysis/jobs/create', testJobData);
      console.log('✅ Status:', createResponse.status);
      console.log('📊 Created job:', JSON.stringify(createResponse.data, null, 2));
      console.log();

      // Test 5: Get specific job details
      if (createResponse.data?.id) {
        console.log('📋 Test 5: Get Specific Job Details');
        console.log(`GET /api/geofence-analysis/jobs/${createResponse.data.id}`);

        const jobDetailResponse = await api.get(`/api/geofence-analysis/jobs/${createResponse.data.id}`);
        console.log('✅ Status:', jobDetailResponse.status);
        console.log('📊 Job details:', JSON.stringify(jobDetailResponse.data, null, 2));
        console.log();
      }

    } else {
      console.log('⚠️  Skipping job creation - no templates or vessels available');
      console.log();
    }

  } catch (error) {
    console.log('❌ Error creating/getting analysis job:');
    console.log('Status:', error.response?.status);
    console.log('Error:', JSON.stringify(error.response?.data, null, 2) || error.message);
    console.log();
  }

  // Test 6: Get fence events
  try {
    console.log('📋 Test 6: Get Fence Events');
    console.log('POST /api/geofence-analysis/fence-events');

    const fenceEventsResponse = await api.post('/api/geofence-analysis/fence-events', {});
    console.log('✅ Status:', fenceEventsResponse.status);
    console.log('📊 Fence events found:', Array.isArray(fenceEventsResponse.data) ? fenceEventsResponse.data.length : 'N/A');
    
    if (Array.isArray(fenceEventsResponse.data) && fenceEventsResponse.data.length > 0) {
      console.log('🎯 First fence event:', {
        id: fenceEventsResponse.data[0].id,
        vesselName: fenceEventsResponse.data[0].vesselName,
        eventType: fenceEventsResponse.data[0].eventType,
        eventTimestamp: fenceEventsResponse.data[0].eventTimestamp
      });
    }
    console.log();

  } catch (error) {
    console.log('❌ Error getting fence events:');
    console.log('Status:', error.response?.status);
    console.log('Error:', error.response?.data || error.message);
    console.log();
  }

  console.log('🎉 Geofence Analysis API testing completed!');
}

// Run the test
testGeofenceAnalysisAPI().catch(console.error);
