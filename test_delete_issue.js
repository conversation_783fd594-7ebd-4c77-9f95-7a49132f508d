#!/usr/bin/env node

/**
 * Test script to investigate WLP template deletion issue
 */

const https = require('https');
const fs = require('fs');

// Test configuration
const API_BASE_URL = 'https://localhost:7001';
const APPLICATION_ID = '03FC0B90-DFAD-11EE-8D86-0800200C9A66';

// Real JWT token provided by user
const REAL_JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLjbSgUD8t4OsO986AbYvl_OVJhsEaSkMOGUYzB3gic';

// Function to generate UUID v4
function uuidv4() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// Function to create request envelope
function createRequestEnvelope(payload, userId = '08ddcb9c-29ab-49d8-a7b9-faccf5caf282') {
  return {
    Header: {
      Id: uuidv4(),
      Application: APPLICATION_ID,
      Bank: "BANK",
      UserId: userId
    },
    Payload: payload
  };
}

// Function to make HTTPS request
function makeRequest(endpoint, data, token) {
  return new Promise((resolve, reject) => {
    const url = new URL(`${API_BASE_URL}${endpoint}`);

    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json'
      },
      rejectUnauthorized: false
    };

    const req = https.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsedData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(JSON.stringify(data));
    req.end();
  });
}

// Test functions
async function listTemplates() {
  console.log('=== LIST TEMPLATES ===');

  const payload = {
    searchTerm: "",
    pageNumber: 1,
    pageSize: 50
  };

  const requestData = createRequestEnvelope(payload);
  const response = await makeRequest('/api/WlpTemplate/list', requestData, REAL_JWT_TOKEN);

  console.log('Status:', response.statusCode);

  if (response.statusCode === 200) {
    const templates = response.data.payload || [];
    console.log(`✅ Found ${templates.length} template(s):`);

    templates.forEach((template, index) => {
      console.log(`  ${index + 1}. ${template.name} (ID: ${template.id})`);
      console.log(`     File: ${template.fileName}, Zones: ${template.zoneCount}`);
      console.log(`     IsDeleted: ${template.isDeleted}, IsActive: ${template.isActive}`);
    });

    return templates;
  } else {
    console.log('❌ Failed to list templates');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return [];
  }
}

async function createSimpleTemplate() {
  console.log('\n=== CREATE SIMPLE TEMPLATE ===');

  // Use the real WLP file that we know works
  const wlpFilePath = './SmartBoat.API/Implementations/GeoFencingService/geofenceData.wlp';

  if (!fs.existsSync(wlpFilePath)) {
    console.log('❌ WLP file not found:', wlpFilePath);
    return null;
  }

  const wlpFileContent = fs.readFileSync(wlpFilePath, 'utf8');
  const base64Content = Buffer.from(wlpFileContent).toString('base64');

  const payload = {
    name: `Delete Test Template ${Date.now()}`,
    description: "Template created to test deletion",
    fileName: "geofenceData.wlp",
    originalFilename: "geofenceData.wlp",
    fileContentBase64: base64Content
  };

  const requestData = createRequestEnvelope(payload);
  const response = await makeRequest('/api/WlpTemplate/create', requestData, REAL_JWT_TOKEN);

  console.log('Status:', response.statusCode);

  if (response.statusCode === 200) {
    console.log('✅ Template created successfully');
    const template = response.data.payload;
    console.log(`Created: ${template.name} (ID: ${template.id})`);
    return template;
  } else {
    console.log('❌ Failed to create template');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return null;
  }
}

async function deleteTemplate(templateId, templateName) {
  console.log(`\n=== DELETE TEMPLATE: ${templateName} ===`);
  console.log('Template ID:', templateId);

  const payload = {
    templateId: templateId
  };

  const requestData = createRequestEnvelope(payload);
  const response = await makeRequest('/api/WlpTemplate/delete', requestData, REAL_JWT_TOKEN);

  console.log('Status:', response.statusCode);
  console.log('Response:', JSON.stringify(response.data, null, 2));

  if (response.statusCode === 200) {
    console.log('✅ Delete request returned success');
    return true;
  } else {
    console.log('❌ Delete request failed');
    return false;
  }
}

async function getTemplate(templateId) {
  console.log(`\n=== GET TEMPLATE AFTER DELETE ===`);
  console.log('Template ID:', templateId);

  const payload = {
    templateId: templateId
  };

  const requestData = createRequestEnvelope(payload);
  const response = await makeRequest('/api/WlpTemplate/get', requestData, REAL_JWT_TOKEN);

  console.log('Status:', response.statusCode);

  if (response.statusCode === 200) {
    console.log('⚠️  Template still accessible after delete!');
    console.log('Template:', JSON.stringify(response.data.payload, null, 2));
    return response.data.payload;
  } else if (response.statusCode === 404) {
    console.log('✅ Template properly deleted (404 Not Found)');
    return null;
  } else {
    console.log('❓ Unexpected response');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return null;
  }
}

// Main test function
async function testDeleteIssue() {
  console.log('🔍 Investigating WLP Template Deletion Issue');
  console.log('='.repeat(50));

  try {
    // 1. List current templates
    console.log('\n1. Current templates in system:');
    const initialTemplates = await listTemplates();

    // 2. Use existing template for deletion test if available
    let testTemplate = null;
    if (initialTemplates.length > 0) {
      testTemplate = initialTemplates[0];
      console.log(`\n2. Using existing template for deletion test: ${testTemplate.name}`);
    } else {
      console.log('\n2. Creating a new template for deletion test:');
      testTemplate = await createSimpleTemplate();

      if (!testTemplate) {
        console.log('❌ Cannot test deletion - template creation failed');
        return;
      }

      // 3. List templates again to confirm creation
      console.log('\n3. Templates after creation:');
      await listTemplates();
    }

    // 4. Delete the template
    console.log('\n4. Deleting the template:');
    const deleteSuccess = await deleteTemplate(testTemplate.id, testTemplate.name);

    // 5. List templates after deletion
    console.log('\n5. Templates after deletion:');
    const finalTemplates = await listTemplates();

    // 6. Try to get the deleted template directly
    console.log('\n6. Attempting to get deleted template directly:');
    await getTemplate(testTemplate.id);

    // 7. Summary
    console.log('\n' + '='.repeat(50));
    console.log('🔍 DELETION TEST SUMMARY:');
    console.log(`   Initial templates: ${initialTemplates.length}`);
    console.log(`   Final templates: ${finalTemplates.length}`);
    console.log(`   Delete API response: ${deleteSuccess ? '✅ Success' : '❌ Failed'}`);

    const templateStillInList = finalTemplates.some(t => t.id === testTemplate.id);
    console.log(`   Template in list after delete: ${templateStillInList ? '❌ YES (ISSUE!)' : '✅ NO'}`);

    if (deleteSuccess && !templateStillInList) {
      console.log('✅ DELETION WORKING CORRECTLY');
    } else {
      console.log('❌ DELETION ISSUE CONFIRMED');

      if (templateStillInList) {
        const stillExists = finalTemplates.find(t => t.id === testTemplate.id);
        console.log('   Template details after "deletion":');
        console.log(`     IsDeleted: ${stillExists.isDeleted}`);
        console.log(`     IsActive: ${stillExists.isActive}`);
      }
    }

  } catch (error) {
    console.error('❌ Test Error:', error.message);
  }
}

// Run the test
testDeleteIssue();
