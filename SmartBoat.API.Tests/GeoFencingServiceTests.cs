using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Hosting;
using Moq;
using SmartBoat.API.Implementations.GeoFencingService;
using SmartBoat.API.Services;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Types;
using Nbg.NetCore.DatabaseService;
using Xunit;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System.IO;
using System.Text.Json;

namespace SmartBoat.API.Tests
{
    public class GeoFencingServiceTests
    {
        private readonly Mock<IDatabaseService> _mockDatabaseService;
        private readonly Mock<ILogger<GeoFencingService>> _mockLogger;
        private readonly Mock<IWebHostEnvironment> _mockEnvironment;
        private readonly GeoFencingService _geoFencingService;

        public GeoFencingServiceTests()
        {
            _mockDatabaseService = new Mock<IDatabaseService>();
            _mockLogger = new Mock<ILogger<GeoFencingService>>();
            
            // Use REAL services instead of mocks!
            var realWlpParser = new WlpGeofenceParser(Mock.Of<ILogger<WlpGeofenceParser>>());
            var realPolygonService = new PolygonGeofenceService(Mock.Of<ILogger<PolygonGeofenceService>>());
            _mockEnvironment = new Mock<IWebHostEnvironment>();

            // Setup real path to WLP file  
            _mockEnvironment.Setup(e => e.ContentRootPath).Returns(
                Path.Combine(Directory.GetCurrentDirectory(), "..", "smartboat-platform", "SmartBoat.API"));

            _geoFencingService = new GeoFencingService(
                _mockDatabaseService.Object, 
                _mockLogger.Object,
                realWlpParser,
                realPolygonService,
                _mockEnvironment.Object);
            
            // Allow time for async WLP file loading
            Task.Delay(1000).Wait();
        }


        #region Distance Calculation Tests

        [Fact]
        public void CalculateDistanceKm_SameCoordinates_ShouldReturnZero()
        {
            // Arrange
            float lat1 = 37.9755f;
            float lng1 = 23.7348f;
            float lat2 = 37.9755f;
            float lng2 = 23.7348f;

            // Act
            var distance = _geoFencingService.CalculateDistanceKm(lat1, lng1, lat2, lng2);

            // Assert
            Assert.True(distance < 0.001, $"Expected distance close to 0, got {distance}");
        }

        [Fact]
        public void CalculateDistanceKm_AthensToThessaloniki_ShouldReturnCorrectDistance()
        {
            // Arrange - Athens to Thessaloniki (approximately 304 km)
            float athensLat = 37.9755f;
            float athensLng = 23.7348f;
            float thessalonikiLat = 40.6401f;
            float thessalonikiLng = 22.9444f;

            // Act
            var distance = _geoFencingService.CalculateDistanceKm(athensLat, athensLng, thessalonikiLat, thessalonikiLng);

            // Assert - Allow some tolerance for calculation precision
            Assert.True(distance > 300 && distance < 310, $"Expected distance around 304km, got {distance}");
        }

        [Fact]
        public void IsInsideGeoFence_TestBasicFunctionality()
        {
            // Arrange - Use some coordinates to test basic functionality
            float lat = 37.90f; 
            float lng = 23.69f;

            // Act - Just test that the method works without throwing exceptions
            var isInside = _geoFencingService.IsInsideGeoFence(lat, lng);

            // Assert - Just verify we get a boolean result
            Assert.True(isInside == true || isInside == false);
        }

        [Fact]
        public void GetGeofenceZoneName_TestBasicFunctionality()
        {
            // Arrange 
            float lat = 37.90f;
            float lng = 23.69f;

            // Act - Just test that the method works without throwing exceptions
            var zoneName = _geoFencingService.GetGeofenceZoneName(lat, lng);

            // Assert - Just verify we get a string result
            Assert.NotNull(zoneName);
        }

        #endregion

        #region GetVesselFenceEvents Tests - No Records

        [Fact]
        public async Task GetVesselFenceEventsAsync_NoRecords_ShouldReturnEmptyList()
        {
            // Arrange
            var vesselId = Guid.NewGuid();
            var request = new GetVesselFenceEventsDto { VesselId = vesselId };
            
            _mockDatabaseService
                .Setup(x => x.SelectAsync<VesselFenceEvent>(It.IsAny<object>()))
                .ReturnsAsync(new List<VesselFenceEvent>());

            // Act
            var result = await _geoFencingService.GetVesselFenceEventsAsync(request);

            // Assert
            Assert.Empty(result);
            _mockDatabaseService.Verify(x => x.SelectAsync<VesselFenceEvent>(It.IsAny<object>()), Times.Once);
        }

        [Fact]
        public async Task GetVesselFenceEventsAsync_DatabaseReturnsNull_ShouldReturnEmptyList()
        {
            // Arrange
            var vesselId = Guid.NewGuid();
            var request = new GetVesselFenceEventsDto { VesselId = vesselId };
            
            _mockDatabaseService
                .Setup(x => x.SelectAsync<VesselFenceEvent>(It.IsAny<object>()))
                .ReturnsAsync((List<VesselFenceEvent>?)null);

            // Act
            var result = await _geoFencingService.GetVesselFenceEventsAsync(request);

            // Assert
            Assert.Empty(result);
        }

        #endregion

        #region GetVesselFenceEvents Tests - One Record

        [Fact]
        public async Task GetVesselFenceEventsAsync_OneActiveRecord_ShouldReturnOneRecord()
        {
            // Arrange
            var vesselId = Guid.NewGuid();
            var eventId = Guid.NewGuid();
            var exitTime = DateTime.UtcNow.AddHours(-2);
            
            var request = new GetVesselFenceEventsDto { VesselId = vesselId };

            var fenceEvent = new VesselFenceEvent
            {
                Id = eventId,
                VesselId = vesselId,
                ExitTimestamp = exitTime,
                ExitLat = 40.6401f, // Outside Athens
                ExitLng = 22.9444f,
                Status = "Active",
                Created = exitTime
            };

            var vessel = new Vessel { Id = vesselId, Name = "Test Vessel" };

            _mockDatabaseService
                .Setup(x => x.SelectAsync<VesselFenceEvent>(It.IsAny<object>()))
                .ReturnsAsync(new List<VesselFenceEvent> { fenceEvent });

            _mockDatabaseService
                .Setup(x => x.SelectByIdAsync<Vessel, Guid>(vesselId))
                .ReturnsAsync(vessel);

            // Act
            var result = await _geoFencingService.GetVesselFenceEventsAsync(request);

            // Assert
            Assert.Single(result);
            var returnedEvent = result.First();
            Assert.Equal(eventId, returnedEvent.Id);
            Assert.Equal(vesselId, returnedEvent.VesselId);
            Assert.Equal("Test Vessel", returnedEvent.VesselName);
            Assert.Equal("Active", returnedEvent.Status);
            Assert.Equal(exitTime, returnedEvent.ExitTimestamp);
            Assert.Null(returnedEvent.EntryTimestamp);
        }

        [Fact]
        public async Task GetVesselFenceEventsAsync_OneCompletedRecord_ShouldReturnOneRecordWithDuration()
        {
            // Arrange
            var vesselId = Guid.NewGuid();
            var eventId = Guid.NewGuid();
            var exitTime = DateTime.UtcNow.AddHours(-3);
            var entryTime = DateTime.UtcNow.AddHours(-1);
            
            var request = new GetVesselFenceEventsDto { VesselId = vesselId };

            var fenceEvent = new VesselFenceEvent
            {
                Id = eventId,
                VesselId = vesselId,
                ExitTimestamp = exitTime,
                ExitLat = 40.6401f,
                ExitLng = 22.9444f,
                EntryTimestamp = entryTime,
                EntryLat = 37.9755f,
                EntryLng = 23.7348f,
                DurationMinutes = 120, // 2 hours
                Status = "Completed",
                Created = exitTime,
                Changed = entryTime
            };

            var vessel = new Vessel { Id = vesselId, Name = "Test Vessel" };

            _mockDatabaseService
                .Setup(x => x.SelectAsync<VesselFenceEvent>(It.IsAny<object>()))
                .ReturnsAsync(new List<VesselFenceEvent> { fenceEvent });

            _mockDatabaseService
                .Setup(x => x.SelectByIdAsync<Vessel, Guid>(vesselId))
                .ReturnsAsync(vessel);

            // Act
            var result = await _geoFencingService.GetVesselFenceEventsAsync(request);

            // Assert
            Assert.Single(result);
            var returnedEvent = result.First();
            Assert.Equal(eventId, returnedEvent.Id);
            Assert.Equal("Completed", returnedEvent.Status);
            Assert.Equal(exitTime, returnedEvent.ExitTimestamp);
            Assert.Equal(entryTime, returnedEvent.EntryTimestamp);
            Assert.Equal(120, returnedEvent.DurationMinutes);
        }

        #endregion

        #region GetVesselFenceEvents Tests - Many Records

        [Fact]
        public async Task GetVesselFenceEventsAsync_ManyRecords_ShouldReturnAllRecordsOrderedByExitTime()
        {
            // Arrange
            var vesselId = Guid.NewGuid();
            var baseTime = DateTime.UtcNow.AddDays(-5);
            
            var request = new GetVesselFenceEventsDto { VesselId = vesselId };

            var fenceEvents = new List<VesselFenceEvent>
            {
                new VesselFenceEvent
                {
                    Id = Guid.NewGuid(),
                    VesselId = vesselId,
                    ExitTimestamp = baseTime.AddHours(1),
                    ExitLat = 40.6401f,
                    ExitLng = 22.9444f,
                    EntryTimestamp = baseTime.AddHours(2),
                    EntryLat = 37.9755f,
                    EntryLng = 23.7348f,
                    DurationMinutes = 60,
                    Status = "Completed",
                    Created = baseTime.AddHours(1)
                },
                new VesselFenceEvent
                {
                    Id = Guid.NewGuid(),
                    VesselId = vesselId,
                    ExitTimestamp = baseTime.AddHours(5), // Later exit time
                    ExitLat = 40.7f,
                    ExitLng = 22.8f,
                    Status = "Active",
                    Created = baseTime.AddHours(5)
                },
                new VesselFenceEvent
                {
                    Id = Guid.NewGuid(),
                    VesselId = vesselId,
                    ExitTimestamp = baseTime.AddHours(3), // Middle exit time
                    ExitLat = 40.5f,
                    ExitLng = 23.1f,
                    EntryTimestamp = baseTime.AddHours(4),
                    EntryLat = 37.9755f,
                    EntryLng = 23.7348f,
                    DurationMinutes = 60,
                    Status = "Completed",
                    Created = baseTime.AddHours(3)
                }
            };

            var vessel = new Vessel { Id = vesselId, Name = "Multi-Event Vessel" };

            _mockDatabaseService
                .Setup(x => x.SelectAsync<VesselFenceEvent>(It.IsAny<object>()))
                .ReturnsAsync(fenceEvents);

            _mockDatabaseService
                .Setup(x => x.SelectByIdAsync<Vessel, Guid>(vesselId))
                .ReturnsAsync(vessel);

            // Act
            var result = await _geoFencingService.GetVesselFenceEventsAsync(request);

            // Assert
            Assert.Equal(3, result.Count);
            
            // Should be ordered by ExitTimestamp descending (latest first)
            Assert.True(result[0].ExitTimestamp > result[1].ExitTimestamp);
            Assert.True(result[1].ExitTimestamp > result[2].ExitTimestamp);
            
            // Verify the latest event is first
            Assert.Equal(baseTime.AddHours(5), result[0].ExitTimestamp);
            Assert.Equal("Active", result[0].Status);
            
            // All should have correct vessel name
            Assert.All(result, r => Assert.Equal("Multi-Event Vessel", r.VesselName));
        }

        [Fact]
        public async Task GetVesselFenceEventsAsync_ManyRecordsWithPagination_ShouldRespectPagination()
        {
            // Arrange
            var vesselId = Guid.NewGuid();
            var baseTime = DateTime.UtcNow.AddDays(-2);
            
            var request = new GetVesselFenceEventsDto 
            { 
                VesselId = vesselId,
                PageOffset = 1,
                PageLimit = 2
            };

            // Create 5 events
            var fenceEvents = Enumerable.Range(1, 5).Select(i => new VesselFenceEvent
            {
                Id = Guid.NewGuid(),
                VesselId = vesselId,
                ExitTimestamp = baseTime.AddHours(i),
                ExitLat = 40.6401f,
                ExitLng = 22.9444f,
                Status = "Completed",
                Created = baseTime.AddHours(i)
            }).ToList();

            var vessel = new Vessel { Id = vesselId, Name = "Paginated Vessel" };

            _mockDatabaseService
                .Setup(x => x.SelectAsync<VesselFenceEvent>(It.IsAny<object>()))
                .ReturnsAsync(fenceEvents);

            _mockDatabaseService
                .Setup(x => x.SelectByIdAsync<Vessel, Guid>(vesselId))
                .ReturnsAsync(vessel);

            // Act
            var result = await _geoFencingService.GetVesselFenceEventsAsync(request);

            // Assert
            Assert.Equal(2, result.Count); // PageLimit = 2
            
            // Should skip first record (PageOffset = 1) and return next 2
            // Since ordered descending, should get the 2nd and 3rd latest events
            Assert.Equal(baseTime.AddHours(4), result[0].ExitTimestamp);
            Assert.Equal(baseTime.AddHours(3), result[1].ExitTimestamp);
        }

        [Fact]
        public async Task GetVesselFenceEventsAsync_WithStatusFilter_ShouldReturnOnlyMatchingRecords()
        {
            // Arrange
            var vesselId = Guid.NewGuid();
            var baseTime = DateTime.UtcNow.AddHours(-5);
            
            var request = new GetVesselFenceEventsDto 
            { 
                VesselId = vesselId,
                Status = "Active"
            };

            var fenceEvents = new List<VesselFenceEvent>
            {
                new VesselFenceEvent
                {
                    Id = Guid.NewGuid(),
                    VesselId = vesselId,
                    ExitTimestamp = baseTime.AddHours(1),
                    Status = "Completed",
                    Created = baseTime.AddHours(1)
                },
                new VesselFenceEvent
                {
                    Id = Guid.NewGuid(),
                    VesselId = vesselId,
                    ExitTimestamp = baseTime.AddHours(2),
                    Status = "Active",
                    Created = baseTime.AddHours(2)
                },
                new VesselFenceEvent
                {
                    Id = Guid.NewGuid(),
                    VesselId = vesselId,
                    ExitTimestamp = baseTime.AddHours(3),
                    Status = "Active",
                    Created = baseTime.AddHours(3)
                }
            };

            var vessel = new Vessel { Id = vesselId, Name = "Filtered Vessel" };

            _mockDatabaseService
                .Setup(x => x.SelectAsync<VesselFenceEvent>(It.IsAny<object>()))
                .ReturnsAsync(fenceEvents);

            _mockDatabaseService
                .Setup(x => x.SelectByIdAsync<Vessel, Guid>(vesselId))
                .ReturnsAsync(vessel);

            // Act
            var result = await _geoFencingService.GetVesselFenceEventsAsync(request);

            // Assert
            Assert.Equal(2, result.Count);
            Assert.All(result, r => Assert.Equal("Active", r.Status));
        }

        [Fact]
        public async Task GetVesselFenceEventsAsync_WithDateRange_ShouldReturnOnlyRecordsInRange()
        {
            // Arrange
            var vesselId = Guid.NewGuid();
            var baseTime = DateTime.UtcNow.AddDays(-10);
            
            var request = new GetVesselFenceEventsDto 
            { 
                VesselId = vesselId,
                FromDate = baseTime.AddDays(2),
                ToDate = baseTime.AddDays(5)
            };

            var fenceEvents = new List<VesselFenceEvent>
            {
                new VesselFenceEvent // Before range
                {
                    Id = Guid.NewGuid(),
                    VesselId = vesselId,
                    ExitTimestamp = baseTime.AddDays(1),
                    Status = "Completed",
                    Created = baseTime.AddDays(1)
                },
                new VesselFenceEvent // In range
                {
                    Id = Guid.NewGuid(),
                    VesselId = vesselId,
                    ExitTimestamp = baseTime.AddDays(3),
                    Status = "Completed",
                    Created = baseTime.AddDays(3)
                },
                new VesselFenceEvent // In range
                {
                    Id = Guid.NewGuid(),
                    VesselId = vesselId,
                    ExitTimestamp = baseTime.AddDays(4),
                    Status = "Active",
                    Created = baseTime.AddDays(4)
                },
                new VesselFenceEvent // After range
                {
                    Id = Guid.NewGuid(),
                    VesselId = vesselId,
                    ExitTimestamp = baseTime.AddDays(7),
                    Status = "Active",
                    Created = baseTime.AddDays(7)
                }
            };

            var vessel = new Vessel { Id = vesselId, Name = "Date Range Vessel" };

            _mockDatabaseService
                .Setup(x => x.SelectAsync<VesselFenceEvent>(It.IsAny<object>()))
                .ReturnsAsync(fenceEvents);

            _mockDatabaseService
                .Setup(x => x.SelectByIdAsync<Vessel, Guid>(vesselId))
                .ReturnsAsync(vessel);

            // Act
            var result = await _geoFencingService.GetVesselFenceEventsAsync(request);

            // Assert
            Assert.Equal(2, result.Count);
            Assert.All(result, r => 
            {
                Assert.True(r.ExitTimestamp >= request.FromDate);
                Assert.True(r.ExitTimestamp <= request.ToDate);
            });
        }

        #endregion

        #region GetActiveFenceEvents Tests

        [Fact]
        public async Task GetActiveFenceEventsAsync_NoActiveEvents_ShouldReturnEmptyList()
        {
            // Arrange
            _mockDatabaseService
                .Setup(x => x.SelectAsync<VesselFenceEvent>(It.IsAny<object>()))
                .ReturnsAsync(new List<VesselFenceEvent>());

            // Act
            var result = await _geoFencingService.GetActiveFenceEventsAsync();

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetActiveFenceEventsAsync_MultipleActiveEvents_ShouldReturnAllOrderedByExitTime()
        {
            // Arrange
            var vessel1Id = Guid.NewGuid();
            var vessel2Id = Guid.NewGuid();
            var baseTime = DateTime.UtcNow.AddHours(-3);

            var activeEvents = new List<VesselFenceEvent>
            {
                new VesselFenceEvent
                {
                    Id = Guid.NewGuid(),
                    VesselId = vessel1Id,
                    ExitTimestamp = baseTime.AddHours(1),
                    ExitLat = 40.6401f,
                    ExitLng = 22.9444f,
                    Status = "Active",
                    Created = baseTime.AddHours(1)
                },
                new VesselFenceEvent
                {
                    Id = Guid.NewGuid(),
                    VesselId = vessel2Id,
                    ExitTimestamp = baseTime.AddHours(2),
                    ExitLat = 40.7f,
                    ExitLng = 22.8f,
                    Status = "Active",
                    Created = baseTime.AddHours(2)
                }
            };

            var vessel1 = new Vessel { Id = vessel1Id, Name = "Active Vessel 1" };
            var vessel2 = new Vessel { Id = vessel2Id, Name = "Active Vessel 2" };

            _mockDatabaseService
                .Setup(x => x.SelectAsync<VesselFenceEvent>(It.IsAny<object>()))
                .ReturnsAsync(activeEvents);

            _mockDatabaseService
                .Setup(x => x.SelectByIdAsync<Vessel, Guid>(vessel1Id))
                .ReturnsAsync(vessel1);

            _mockDatabaseService
                .Setup(x => x.SelectByIdAsync<Vessel, Guid>(vessel2Id))
                .ReturnsAsync(vessel2);

            // Act
            var result = await _geoFencingService.GetActiveFenceEventsAsync();

            // Assert
            Assert.Equal(2, result.Count);
            
            // Should be ordered by ExitTimestamp descending
            Assert.True(result[0].ExitTimestamp > result[1].ExitTimestamp);
            Assert.Equal("Active Vessel 2", result[0].VesselName);
            Assert.Equal("Active Vessel 1", result[1].VesselName);
            
            Assert.All(result, r => Assert.Equal("Active", r.Status));
        }

        #endregion

        #region Real-World Coordinate Data Tests

        public class TestCoordinate
        {
            public double Lat { get; set; }
            public double Lng { get; set; }
        }

        private List<TestCoordinate> LoadCoordinatesFromFile(string fileName)
        {
            var filePath = Path.Combine(AppContext.BaseDirectory, fileName);
            if (!File.Exists(filePath))
            {
                // Try relative path
                filePath = fileName;
            }
            
            var jsonContent = File.ReadAllText(filePath);
            return JsonSerializer.Deserialize<List<TestCoordinate>>(jsonContent) ?? new List<TestCoordinate>();
        }

        [Fact]
        public void RealWorldCoordinates_ShouldNotHaveGoneOutsideFence_AllCoordinatesShouldBeInsideFence()
        {
            // Arrange
            var coordinates = LoadCoordinatesFromFile("testCoordsShouldNotHaveGoneOutsideFence.json");
            
            // Assert we have test data
            Assert.True(coordinates.Count > 0, "Test coordinate file should contain coordinates");

            // Act & Assert - Test each coordinate
            var outsideCoordinates = new List<TestCoordinate>();
            foreach (var coord in coordinates)
            {
                var isInside = _geoFencingService.IsInsideGeoFence((float)coord.Lat, (float)coord.Lng);
                if (!isInside)
                {
                    outsideCoordinates.Add(coord);
                }
            }

            // Assert
            Assert.True(outsideCoordinates.Count == 0, 
                $"All coordinates should be inside the fence. Found {outsideCoordinates.Count} outside coordinates. " +
                $"First few outside: {string.Join(", ", outsideCoordinates.Take(5).Select(c => $"({c.Lat}, {c.Lng})"))}");
        }

        [Fact]
        public void RealWorldCoordinates_ShouldHaveGoneOutsideFence_AllCoordinatesShouldBeOutsideFence()
        {
            // Arrange
            var coordinates = LoadCoordinatesFromFile("testCoordsShouldHaveGoneOutsideFence.json");
            
            // Assert we have test data
            Assert.True(coordinates.Count > 0, "Test coordinate file should contain coordinates");

            // Act & Assert - Test each coordinate
            var insideCoordinates = new List<TestCoordinate>();
            foreach (var coord in coordinates)
            {
                var isInside = _geoFencingService.IsInsideGeoFence((float)coord.Lat, (float)coord.Lng);
                if (isInside)
                {
                    insideCoordinates.Add(coord);
                }
            }

            // Assert
            Assert.True(insideCoordinates.Count == 0, 
                $"All coordinates should be outside the fence. Found {insideCoordinates.Count} inside coordinates. " +
                $"First few inside: {string.Join(", ", insideCoordinates.Take(5).Select(c => $"({c.Lat}, {c.Lng})"))}");
        }

        [Fact]
        public void RealWorldCoordinates_InsideFenceData_SampleCoordinateVerification()
        {
            // Arrange
            var coordinates = LoadCoordinatesFromFile("testCoordsShouldNotHaveGoneOutsideFence.json");
            Assert.True(coordinates.Count > 0, "Test coordinate file should contain coordinates");

            // Act - Test a sample of coordinates and their fence status
            var sampleCoordinates = coordinates.Take(10).ToList();
            var fenceResults = new List<(TestCoordinate coord, bool isInside, string zoneName)>();

            foreach (var coord in sampleCoordinates)
            {
                var isInside = _geoFencingService.IsInsideGeoFence((float)coord.Lat, (float)coord.Lng);
                var zoneName = _geoFencingService.GetGeofenceZoneName((float)coord.Lat, (float)coord.Lng);
                fenceResults.Add((coord, isInside, zoneName));
            }

            // Assert - All should be inside some territory
            foreach (var result in fenceResults)
            {
                Assert.True(result.isInside, 
                    $"Coordinate ({result.coord.Lat}, {result.coord.Lng}) should be inside a geofence territory. Zone: {result.zoneName}");
                Assert.NotEqual("Outside all zones", result.zoneName);
            }
        }

        [Fact]
        public void RealWorldCoordinates_OutsideFenceData_SampleCoordinateVerification()
        {
            // Arrange
            var coordinates = LoadCoordinatesFromFile("testCoordsShouldHaveGoneOutsideFence.json");
            Assert.True(coordinates.Count > 0, "Test coordinate file should contain coordinates");

            // Act - Test a sample of coordinates and their fence status
            var sampleCoordinates = coordinates.Take(10).ToList();
            var fenceResults = new List<(TestCoordinate coord, bool isInside, string zoneName)>();

            foreach (var coord in sampleCoordinates)
            {
                var isInside = _geoFencingService.IsInsideGeoFence((float)coord.Lat, (float)coord.Lng);
                var zoneName = _geoFencingService.GetGeofenceZoneName((float)coord.Lat, (float)coord.Lng);
                fenceResults.Add((coord, isInside, zoneName));
            }

            // Assert - All should be outside all territories
            foreach (var result in fenceResults)
            {
                Assert.False(result.isInside, 
                    $"Coordinate ({result.coord.Lat}, {result.coord.Lng}) should be outside all geofence territories. Zone: {result.zoneName}");
                Assert.Equal("Outside all zones", result.zoneName);
            }
        }

        [Fact]
        public async Task SimulateVesselJourney_InsideFenceCoordinates_ShouldNotCreateFenceEvents()
        {
            // Arrange
            var coordinates = LoadCoordinatesFromFile("testCoordsShouldNotHaveGoneOutsideFence.json");
            var vesselId = Guid.NewGuid();
            var baseTime = DateTime.UtcNow.AddHours(-2);

            // Mock database to return empty active events
            _mockDatabaseService
                .Setup(x => x.SelectAsync<VesselFenceEvent>(It.IsAny<object>()))
                .ReturnsAsync(new List<VesselFenceEvent>());

            // Act - Simulate a vessel journey through inside coordinates
            var sampleCoordinates = coordinates.Take(100).ToList(); // Use first 100 coordinates
            for (int i = 0; i < sampleCoordinates.Count; i++)
            {
                var coord = sampleCoordinates[i];
                var timestamp = baseTime.AddMinutes(i * 5); // 5 minute intervals
                
                await _geoFencingService.CheckVesselPositionAsync(
                    vesselId, 
                    (float)coord.Lat, 
                    (float)coord.Lng, 
                    timestamp);
            }

            // Assert - No fence events should be created (vessel never left the fence)
            _mockDatabaseService.Verify(x => x.InsertAsync<VesselFenceEvent>(It.IsAny<VesselFenceEvent>()), Times.Never);
            _mockDatabaseService.Verify(x => x.UpdateAsync<VesselFenceEvent>(It.IsAny<VesselFenceEvent>(), It.IsAny<object>()), Times.Never);
        }

        [Fact]
        public async Task SimulateVesselJourney_OutsideFenceCoordinates_ShouldCreateFenceEvent()
        {
            // Arrange
            var coordinates = LoadCoordinatesFromFile("testCoordsShouldHaveGoneOutsideFence.json");
            var vesselId = Guid.NewGuid();
            var baseTime = DateTime.UtcNow.AddHours(-2);

            // Mock database to return empty active events initially
            _mockDatabaseService
                .Setup(x => x.SelectAsync<VesselFenceEvent>(It.IsAny<object>()))
                .ReturnsAsync(new List<VesselFenceEvent>());

            // Act - Simulate vessel starting at the first outside coordinate
            var firstCoord = coordinates.First();
            await _geoFencingService.CheckVesselPositionAsync(
                vesselId, 
                (float)firstCoord.Lat, 
                (float)firstCoord.Lng, 
                baseTime);

            // Assert - One fence event should be created (vessel is outside)
            _mockDatabaseService.Verify(x => x.InsertAsync<VesselFenceEvent>(It.IsAny<VesselFenceEvent>()), Times.Once);
        }

        #endregion
    }
}