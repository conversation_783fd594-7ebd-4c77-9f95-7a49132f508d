#!/usr/bin/env node

/**
 * Test script to verify the GeofenceAnalysisController envelope format fix
 */

const https = require('https');

// Test configuration
const API_BASE_URL = 'https://localhost:7001';
const APPLICATION_ID = '03FC0B90-DFAD-11EE-8D86-0800200C9A66';

// Real JWT token provided by user
const REAL_JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLjbSgUD8t4OsO986AbYvl_OVJhsEaSkMOGUYzB3gic';

// Function to generate UUID v4
function uuidv4() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// Function to create request envelope
function createRequestEnvelope(payload, userId = '08ddcb9c-29ab-49d8-a7b9-faccf5caf282') {
  return {
    Header: {
      Id: uuidv4(),
      Application: APPLICATION_ID,
      Bank: "BANK",
      UserId: userId
    },
    Payload: payload
  };
}

// Function to make HTTPS request
function makeRequest(endpoint, data, token) {
  return new Promise((resolve, reject) => {
    const url = new URL(`${API_BASE_URL}${endpoint}`);
    
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json'
      },
      rejectUnauthorized: false
    };

    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsedData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(JSON.stringify(data));
    req.end();
  });
}

// Test functions
async function testCreateAnalysisJob() {
  console.log('=== CREATE ANALYSIS JOB TEST ===');
  
  const payload = {
    vesselId: "6012d382-b76e-4e42-87df-a01dd4eb9857",
    templateIds: ["bba8c338-fdeb-49aa-a957-32c72beed11d"],
    analysisDateFrom: "2025-08-16T00:00:00.000Z",
    analysisDateTo: "2025-09-15T23:59:59.999Z",
    name: "Test Analysis Job - Fixed Envelope"
  };
  
  const requestData = createRequestEnvelope(payload);
  
  console.log('📤 Request payload:');
  console.log(JSON.stringify(requestData, null, 2));
  console.log();
  
  const response = await makeRequest('/api/geofence-analysis/jobs/create', requestData, REAL_JWT_TOKEN);
  
  console.log('📥 Response status:', response.statusCode);
  console.log('📥 Response data:');
  console.log(JSON.stringify(response.data, null, 2));
  
  if (response.statusCode === 200) {
    console.log('✅ SUCCESS: Analysis job created successfully!');
    console.log('✅ The envelope format fix is working correctly');
    return response.data.payload;
  } else if (response.statusCode === 400) {
    console.log('❌ FAILED: Still getting 400 error');
    console.log('❌ The envelope format fix may not be working');
    return null;
  } else {
    console.log(`⚠️  UNEXPECTED: Got status code ${response.statusCode}`);
    return null;
  }
}

async function testGetAnalysisJobs() {
  console.log('\n=== GET ANALYSIS JOBS TEST ===');
  
  const payload = {};
  const requestData = createRequestEnvelope(payload);
  const response = await makeRequest('/api/geofence-analysis/jobs/list', requestData, REAL_JWT_TOKEN);
  
  console.log('📥 Response status:', response.statusCode);
  
  if (response.statusCode === 200) {
    console.log('✅ SUCCESS: Analysis jobs list retrieved successfully!');
    console.log(`📊 Found ${response.data.payload?.length || 0} analysis jobs`);
    return true;
  } else {
    console.log('❌ FAILED: Could not retrieve analysis jobs');
    console.log('📥 Response:', JSON.stringify(response.data, null, 2));
    return false;
  }
}

// Main test function
async function testGeofenceAnalysisFix() {
  console.log('🧪 Testing Geofence Analysis Controller Envelope Format Fix');
  console.log('='.repeat(70));
  
  try {
    // Test creating an analysis job with the corrected envelope format
    const createdJob = await testCreateAnalysisJob();
    
    // Test getting analysis jobs list
    const listSuccess = await testGetAnalysisJobs();
    
    console.log('\n' + '='.repeat(70));
    console.log('🏁 GEOFENCE ANALYSIS FIX TEST SUMMARY:');
    console.log(`   Create job: ${createdJob ? '✅ Working' : '❌ Failed'}`);
    console.log(`   List jobs: ${listSuccess ? '✅ Working' : '❌ Failed'}`);
    
    if (createdJob && listSuccess) {
      console.log('\n🎉 SUCCESS: GeofenceAnalysisController envelope format fix is working!');
      console.log('   - The controller now properly accepts Request<T> envelope format');
      console.log('   - Frontend requests should work correctly now');
      console.log('   - The "Vessel ID is required" error should be resolved');
    } else {
      console.log('\n❌ ISSUE: Some tests failed - the fix may need additional work');
    }
    
  } catch (error) {
    console.error('❌ Test Error:', error.message);
  }
}

// Run the test
testGeofenceAnalysisFix();
