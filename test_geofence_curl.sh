#!/bin/bash

# Geofence Analysis API Testing Script (uses JWT and correct payloads)
echo "🚀 Testing Geofence Analysis API endpoints..."
echo

API_BASE_URL="${API_BASE_URL:-https://localhost:7001}"

# Token precedence: CLI arg > TOKEN env > default hardcoded (from user)
JWT_TOKEN="${1:-${TOKEN:-eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.RyPSZU2y8ykey7iA7jEJo0WFuRbO_MMc9pnxnbj5ZdM}}"

if [ -z "$JWT_TOKEN" ]; then
  echo "❌ Missing JWT token. Pass it as first argument or set TOKEN env."
  exit 1
fi

# Helper: make API calls
make_api_call() {
  local method=$1
  local endpoint=$2
  local data=$3
  local description=$4

  echo "📋 Test: $description"
  echo "$method $endpoint"
  echo

  if [ "$method" = "POST" ]; then
    response=$(curl -k -s -w "\nHTTP_STATUS:%{http_code}" \
      -X POST \
      -H "Authorization: Bearer $JWT_TOKEN" \
      -H "Content-Type: application/json" \
      -d "$data" \
      "$API_BASE_URL$endpoint")
  else
    response=$(curl -k -s -w "\nHTTP_STATUS:%{http_code}" \
      -X "$method" \
      -H "Authorization: Bearer $JWT_TOKEN" \
      -H "Content-Type: application/json" \
      "$API_BASE_URL$endpoint")
  fi

  http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
  response_body=$(echo "$response" | sed '/HTTP_STATUS:/d')

  if [ "$http_status" -ge 200 ] && [ "$http_status" -lt 300 ]; then
    echo "✅ Status: $http_status"
    echo "📊 Response:"
    echo "$response_body" | python3 -m json.tool 2>/dev/null || echo "$response_body"
  else
    echo "❌ Status: $http_status"
    echo "📊 Error Response:"
    echo "$response_body" | python3 -m json.tool 2>/dev/null || echo "$response_body"
  fi
  echo
  echo "---"
  echo

  # Export last response body to caller via global var
  LAST_BODY="$response_body"
  LAST_STATUS="$http_status"
}

# 1) Get Analysis Jobs List (raw JSON body; no envelope)
make_api_call "POST" "/api/geofence-analysis/jobs/list" "{}" "Get Analysis Jobs List"

# 2) Get WLP Templates (enveloped)
make_api_call "POST" "/api/WlpTemplate/list" '{"Header":{},"Payload":{}}' "Get WLP Templates"
TEMPLATE_ID=$(python3 - <<'PY'
import sys, json
try:
  data=json.loads(sys.stdin.read())
  payload=data.get('payload') or data.get('Payload')
  if isinstance(payload, list) and payload:
    print(payload[0].get('id') or payload[0].get('Id') or '')
  else:
    print('')
except Exception:
  print('')
PY
<<<"$LAST_BODY")
echo "🔎 Selected TEMPLATE_ID: ${TEMPLATE_ID:-<none>}"
echo

# 3) Get Vessels (enveloped)
make_api_call "POST" "/api/vessel/list" '{"Header":{},"Payload":{"pageLimit":25,"pageOffset":0,"sortField":"name","sortOrder":"ASC"}}' "Get Vessels List"
VESSEL_ID=$(python3 - <<'PY'
import sys, json
try:
  data=json.loads(sys.stdin.read())
  payload=data.get('payload') or data.get('Payload') or {}
  vessels=(payload.get('data') or payload.get('Data') or [])
  if isinstance(vessels, list) and vessels:
    v=vessels[0]
    print(v.get('id') or v.get('Id') or '')
  else:
    print('')
except Exception:
  print('')
PY
<<<"$LAST_BODY")
echo "🔎 Selected VESSEL_ID: ${VESSEL_ID:-<none>}"
echo

# 4) Get Fence Events (raw JSON body; no envelope)
make_api_call "POST" "/api/geofence-analysis/fence-events" "{}" "Get Fence Events"

# 5) Create Analysis Job (raw JSON body)
if [ -n "$VESSEL_ID" ] && [ -n "$TEMPLATE_ID" ]; then
  create_job_data=$(cat <<JSON
{
  "vesselId": "$VESSEL_ID",
  "templateIds": ["$TEMPLATE_ID"],
  "analysisDateFrom": "2024-01-01T00:00:00.000Z",
  "analysisDateTo": "2024-01-31T23:59:59.999Z",
  "name": "API Test Analysis Job"
}
JSON
)
  make_api_call "POST" "/api/geofence-analysis/jobs/create" "$create_job_data" "Create Analysis Job (using first vessel/template)"
else
  echo "⚠️  Skipping job creation — no vessel or template available"
  echo
fi

echo "🎉 Geofence Analysis API testing completed!"
