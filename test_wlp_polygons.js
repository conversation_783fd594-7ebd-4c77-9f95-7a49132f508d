#!/usr/bin/env node

/**
 * Test script to verify WLP Template polygon data endpoint
 * Tests that the WLP file was properly parsed and polygon data stored
 */

const https = require('https');
const crypto = require('crypto');

// Test configuration
const API_BASE_URL = 'https://localhost:7001';
const APPLICATION_ID = '03FC0B90-DFAD-11EE-8D86-0800200C9A66';
const TEMPLATE_ID = '2d9aca52-7e03-49ea-adbc-ca010b35a291'; // From previous test

// JWT configuration from appsettings.Development.json
const JWT_CONFIG = {
  secretKey: 'SmartBoatSecretKeyForJWTTokenGeneration2024!@#$%^&*()_+',
  issuer: 'SmartBoat.API',
  audience: 'SmartBoat.API',
  expirationMinutes: 60
};

// Function to create a valid JWT token
function createValidJwtToken(userId = '********-1234-1234-1234-********9012') {
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  };

  const payload = {
    'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier': userId,
    'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress': '<EMAIL>',
    'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name': 'testuser',
    'http://schemas.microsoft.com/ws/2008/06/identity/claims/role': 'Administrator',
    'FirstName': 'Test',
    'LastName': 'User',
    iss: JWT_CONFIG.issuer,
    aud: JWT_CONFIG.audience,
    exp: Math.floor(Date.now() / 1000) + (JWT_CONFIG.expirationMinutes * 60),
    iat: Math.floor(Date.now() / 1000)
  };

  const encodedHeader = Buffer.from(JSON.stringify(header)).toString('base64url');
  const encodedPayload = Buffer.from(JSON.stringify(payload)).toString('base64url');

  const signature = crypto
    .createHmac('sha256', JWT_CONFIG.secretKey)
    .update(`${encodedHeader}.${encodedPayload}`)
    .digest('base64url');

  return `${encodedHeader}.${encodedPayload}.${signature}`;
}

// Create request envelope in the correct format
const createRequestEnvelope = (payload) => {
  return {
    Header: {
      Id: crypto.randomUUID(),
      Application: APPLICATION_ID,
      Bank: "BANK",
      UserId: "********-1234-1234-1234-********9012" // Same user ID as creation
    },
    Payload: payload
  };
};

// Test payload for polygon data request
const testPayload = {
  templateId: TEMPLATE_ID
};

const requestBody = JSON.stringify(createRequestEnvelope(testPayload));

console.log('Testing WLP Template Polygon Data Endpoint...');
console.log('Request URL:', `${API_BASE_URL}/api/WlpTemplate/polygons`);
console.log('Template ID:', TEMPLATE_ID);

const options = {
  hostname: 'localhost',
  port: 7001,
  path: '/api/WlpTemplate/polygons',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(requestBody),
    'Authorization': `Bearer ${createValidJwtToken()}` // Valid JWT token
  },
  rejectUnauthorized: false // Accept self-signed certificates
};

const req = https.request(options, (res) => {
  console.log('\n--- Response ---');
  console.log('Status Code:', res.statusCode);

  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    console.log('\nResponse Body:');
    try {
      const jsonResponse = JSON.parse(data);
      console.log(JSON.stringify(jsonResponse, null, 2));

      if (res.statusCode === 200) {
        console.log('\n✅ SUCCESS: Polygon data request completed successfully!');
        if (jsonResponse.payload && jsonResponse.payload.length > 0) {
          console.log(`🗺️  Found ${jsonResponse.payload.length} polygon(s) in template`);
          jsonResponse.payload.forEach((polygon, index) => {
            console.log(`   ${index + 1}. Zone: ${polygon.zoneName}`);
            console.log(`      Template ID: ${polygon.templateId}`);
            console.log(`      Bounds: Lat ${polygon.minLat} to ${polygon.maxLat}`);
            console.log(`              Lng ${polygon.minLng} to ${polygon.maxLng}`);

            // Parse and display first few coordinate points
            try {
              const polygonData = JSON.parse(polygon.polygonData);
              if (polygonData.length > 0) {
                console.log(`      Points: ${polygonData.length}`);
                console.log(`      First point: (${polygonData[0].Longitude}, ${polygonData[0].Latitude})`);
                if (polygonData.length > 1) {
                  console.log(`      Last point:  (${polygonData[polygonData.length-1].Longitude}, ${polygonData[polygonData.length-1].Latitude})`);
                }
              }
            } catch (e) {
              console.log(`      Polygon data: ${polygon.polygonData.substring(0, 100)}...`);
            }
          });
        } else {
          console.log('🗺️  No polygon data found for template');
        }
      } else {
        console.log(`\n⚠️  UNEXPECTED STATUS: ${res.statusCode}`);
      }
    } catch (error) {
      console.log('Raw response:', data);
      console.log('Parse error:', error.message);
    }
  });
});

req.on('error', (error) => {
  console.error('\n❌ REQUEST ERROR:', error.message);
  if (error.code === 'ECONNREFUSED') {
    console.log('Make sure the SmartBoat API is running on https://localhost:7001');
  }
});

req.write(requestBody);
req.end();
