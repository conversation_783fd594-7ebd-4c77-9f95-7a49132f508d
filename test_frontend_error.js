#!/usr/bin/env node

/**
 * Test script to simulate frontend WLP template service error handling
 */

const fs = require('fs');

// Mock the apiClient to simulate the error response
const mockApiClient = {
  async post(endpoint, data) {
    // Simulate the error response structure that apiClient.js would create
    const error = new Error('Bad Request');
    error.status = 400;
    error.data = {
      "payload": null,
      "exception": {
        "id": null,
        "code": null,
        "description": "A template with identical content already exists: Κεφαλονια",
        "category": null,
        "targetSite": null,
        "message": "Exception of type 'SmartBoat.API.ResponseException' was thrown.",
        "data": {},
        "innerException": null,
        "helpLink": null,
        "source": null,
        "hResult": -2146233088,
        "stackTrace": null
      }
    };
    throw error;
  }
};

// Simulate the WlpTemplateService createTemplate method with improved error handling
class WlpTemplateService {
  constructor() {
    this.endpoint = '/api/WlpTemplate';
  }

  arrayBufferToBase64(buffer) {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  async createTemplate(template) {
    try {
      // Convert ArrayBuffer to base64
      const base64Content = this.arrayBufferToBase64(template.fileContent);
      
      const response = await mockApiClient.post(`${this.endpoint}/create`, {
        name: template.name,
        description: template.description,
        fileName: template.fileName,
        originalFilename: template.originalFilename || template.fileName,
        fileContentBase64: base64Content
      });

      return response || null;
    } catch (error) {
      console.error('Error creating WLP template:', error);
      
      // Extract the specific error message from the backend response
      let errorMessage = 'Failed to create WLP template';
      
      if (error.data?.exception?.description) {
        // Backend returned a structured error with description
        errorMessage = error.data.exception.description;
      } else if (error.response?.data?.exception?.description) {
        // Alternative error structure
        errorMessage = error.response.data.exception.description;
      } else if (error.message) {
        // Generic error message
        errorMessage = error.message;
      }
      
      throw new Error(errorMessage);
    }
  }
}

// Test the improved error handling
async function testFrontendErrorHandling() {
  console.log('Testing Frontend WLP Template Service Error Handling...');
  
  try {
    const wlpTemplateService = new WlpTemplateService();
    
    // Create a mock template with file content
    const mockFileContent = new ArrayBuffer(100); // Mock file content
    
    const template = {
      name: "Test Template",
      description: "Test description",
      fileName: "test.wlp",
      originalFilename: "test.wlp",
      fileContent: mockFileContent
    };
    
    console.log('Attempting to create template (should fail with duplicate error)...');
    
    await wlpTemplateService.createTemplate(template);
    
    console.log('❌ ERROR: Expected an error to be thrown');
    
  } catch (error) {
    console.log('\n--- Caught Error ---');
    console.log('Error message:', error.message);
    
    if (error.message === 'A template with identical content already exists: Κεφαλονια') {
      console.log('\n✅ SUCCESS: Frontend service correctly extracted backend error message!');
      console.log('✅ User will now see: "A template with identical content already exists: Κεφαλονια"');
      console.log('✅ This is much better than the generic "Failed to create WLP template" message');
    } else {
      console.log('\n❌ ERROR: Frontend service did not extract the correct error message');
      console.log('Expected: "A template with identical content already exists: Κεφαλονια"');
      console.log('Got:', error.message);
    }
  }
}

// Run the test
testFrontendErrorHandling();
