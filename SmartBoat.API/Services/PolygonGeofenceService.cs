using Microsoft.Extensions.Logging;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Types;

namespace SmartBoat.API.Services
{
    public class PolygonGeofenceService : IPolygonGeofenceService
    {
        private readonly ILogger<PolygonGeofenceService> _logger;

        public PolygonGeofenceService(ILogger<PolygonGeofenceService> logger)
        {
            _logger = logger;
        }

        public bool IsPointInPolygon(double latitude, double longitude, List<GeofencePoint> polygonPoints)
        {
            if (polygonPoints.Count < 3)
            {
                return false;
            }

            return IsPointInPolygonRayCasting(latitude, longitude, polygonPoints);
        }

        public GeofencePolygon? FindContainingPolygon(double latitude, double longitude, List<GeofencePolygon> polygons)
        {
            foreach (var polygon in polygons)
            {
                if (IsPointInPolygon(latitude, longitude, polygon.Points))
                {
                    _logger.LogDebug("Point ({Lat}, {Lng}) is inside polygon: {PolygonName}", 
                        latitude, longitude, polygon.Name);
                    return polygon;
                }
            }

            _logger.LogDebug("Point ({Lat}, {Lng}) is not inside any polygon", latitude, longitude);
            return null;
        }

        private bool IsPointInPolygonRayCasting(double latitude, double longitude, List<GeofencePoint> polygonPoints)
        {
            bool isInside = false;
            int pointCount = polygonPoints.Count;

            for (int i = 0, j = pointCount - 1; i < pointCount; j = i++)
            {
                var pi = polygonPoints[i];
                var pj = polygonPoints[j];

                if (((pi.Latitude > latitude) != (pj.Latitude > latitude)) &&
                    (longitude < (pj.Longitude - pi.Longitude) * (latitude - pi.Latitude) / (pj.Latitude - pi.Latitude) + pi.Longitude))
                {
                    isInside = !isInside;
                }
            }

            return isInside;
        }

        public double CalculateMinDistanceToPolygon(double latitude, double longitude, List<GeofencePoint> polygonPoints)
        {
            if (polygonPoints.Count == 0)
                return double.MaxValue;

            double minDistance = double.MaxValue;

            for (int i = 0; i < polygonPoints.Count; i++)
            {
                var p1 = polygonPoints[i];
                var p2 = polygonPoints[(i + 1) % polygonPoints.Count];

                double distance = DistanceFromPointToLineSegment(latitude, longitude, p1.Latitude, p1.Longitude, p2.Latitude, p2.Longitude);
                minDistance = Math.Min(minDistance, distance);
            }

            return minDistance;
        }

        private double DistanceFromPointToLineSegment(double pointLat, double pointLng, double lineLat1, double lineLng1, double lineLat2, double lineLng2)
        {
            double A = pointLat - lineLat1;
            double B = pointLng - lineLng1;
            double C = lineLat2 - lineLat1;
            double D = lineLng2 - lineLng1;

            double dot = A * C + B * D;
            double lenSq = C * C + D * D;

            if (lenSq == 0)
            {
                return CalculateDistanceKm(pointLat, pointLng, lineLat1, lineLng1);
            }

            double param = dot / lenSq;

            double xx, yy;

            if (param < 0)
            {
                xx = lineLat1;
                yy = lineLng1;
            }
            else if (param > 1)
            {
                xx = lineLat2;
                yy = lineLng2;
            }
            else
            {
                xx = lineLat1 + param * C;
                yy = lineLng1 + param * D;
            }

            return CalculateDistanceKm(pointLat, pointLng, xx, yy);
        }

        private double CalculateDistanceKm(double lat1, double lng1, double lat2, double lng2)
        {
            const double EarthRadiusKm = 6371.0;

            var lat1Rad = lat1 * Math.PI / 180.0;
            var lat2Rad = lat2 * Math.PI / 180.0;
            var deltaLatRad = (lat2 - lat1) * Math.PI / 180.0;
            var deltaLngRad = (lng2 - lng1) * Math.PI / 180.0;

            var a = Math.Sin(deltaLatRad / 2.0) * Math.Sin(deltaLatRad / 2.0) +
                    Math.Cos(lat1Rad) * Math.Cos(lat2Rad) *
                    Math.Sin(deltaLngRad / 2.0) * Math.Sin(deltaLngRad / 2.0);

            var c = 2.0 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1.0 - a));
            var distance = EarthRadiusKm * c;

            return distance;
        }
    }
}