using System.Text.Json;
using Microsoft.Extensions.Logging;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Types;

namespace SmartBoat.API.Services
{
    public class WlpGeofenceParser : IWlpGeofenceParser
    {
        private readonly ILogger<WlpGeofenceParser> _logger;

        public WlpGeofenceParser(ILogger<WlpGeofenceParser> logger)
        {
            _logger = logger;
        }

        public async Task<WlpGeofenceData?> ParseWlpFileAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    _logger.LogError("WLP file not found at path: {FilePath}", filePath);
                    return null;
                }

                var jsonContent = await File.ReadAllTextAsync(filePath);
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                var wlpData = JsonSerializer.Deserialize<WlpGeofenceData>(jsonContent, options);
                
                if (wlpData != null)
                {
                    _logger.LogInformation("Successfully parsed WLP file with {ZoneCount} zones", wlpData.Zones.Count);
                    foreach (var zone in wlpData.Zones)
                    {
                        _logger.LogDebug("Zone: {ZoneName} ({ZoneId}) with {PointCount} points", 
                            zone.Name, zone.Id, zone.Points.Count);
                    }
                }

                return wlpData;
            }
            catch (JsonException jsonEx)
            {
                _logger.LogError(jsonEx, "Failed to parse WLP file as JSON: {FilePath}", filePath);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error parsing WLP file: {FilePath}", filePath);
                return null;
            }
        }

        public async Task<WlpGeofenceData?> ParseWlpContentAsync(byte[] content)
        {
            try
            {
                var jsonContent = System.Text.Encoding.UTF8.GetString(content);
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                var wlpData = JsonSerializer.Deserialize<WlpGeofenceData>(jsonContent, options);
                
                if (wlpData != null)
                {
                    _logger.LogInformation("Successfully parsed WLP content with {ZoneCount} zones", wlpData.Zones.Count);
                    foreach (var zone in wlpData.Zones)
                    {
                        _logger.LogDebug("Zone: {ZoneName} ({ZoneId}) with {PointCount} points", 
                            zone.Name, zone.Id, zone.Points.Count);
                    }
                }

                return wlpData;
            }
            catch (JsonException jsonEx)
            {
                _logger.LogError(jsonEx, "Failed to parse WLP content as JSON");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error parsing WLP content");
                return null;
            }
        }

        public List<GeofencePolygon> ConvertToGeofencePolygons(WlpGeofenceData wlpData)
        {
            var polygons = new List<GeofencePolygon>();

            foreach (var zone in wlpData.Zones)
            {
                if (zone.Points.Count < 3)
                {
                    _logger.LogWarning("Zone {ZoneName} has insufficient points ({PointCount}) to form a polygon", 
                        zone.Name, zone.Points.Count);
                    continue;
                }

                var polygon = new GeofencePolygon
                {
                    Id = zone.Id,
                    Name = zone.Name,
                    Description = zone.Description,
                    Points = zone.Points.Select(p => new GeofencePoint 
                    { 
                        Longitude = p.X, 
                        Latitude = p.Y,
                        Radius = p.Radius
                    }).ToList()
                };

                polygons.Add(polygon);
                _logger.LogDebug("Converted zone {ZoneName} to polygon with {PointCount} points", 
                    zone.Name, polygon.Points.Count);
            }

            return polygons;
        }
    }
}