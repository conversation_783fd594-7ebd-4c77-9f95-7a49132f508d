-- Geofence Job Templates Table - Track which templates were used in each analysis job
CREATE TABLE GeofenceJobTemplates (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    JobId UNIQUEIDENTIFIER NOT NULL, -- Foreign key to GeofenceAnalysisJobs table
    TemplateId UNIQUEIDENTIFIER NOT NULL, -- Foreign key to WlpTemplates table
    
    -- Audit fields
    Created DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER NOT NULL,
    
    -- Foreign key constraints
    FOREIGN KEY (JobId) REFERENCES GeofenceAnalysisJobs(Id) ON DELETE CASCADE,
    FOR<PERSON><PERSON><PERSON> KEY (TemplateId) REFERENCES WlpTemplates(Id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX IX_GeofenceJobTemplates_JobId (JobId),
    INDEX IX_GeofenceJobTemplates_TemplateId (TemplateId),
    
    -- Unique constraint to prevent duplicate template assignments per job
    UNIQUE (JobId, TemplateId)
);