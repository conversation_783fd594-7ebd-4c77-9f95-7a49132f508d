-- Geofence Analysis Jobs Table - Track analysis jobs
CREATE TABLE GeofenceAnalysisJobs (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    VesselId UNIQUEIDENTIFIER NOT NULL, -- Foreign key to Vessels table
    AnalysisDateFrom DATETIME2 NOT NULL, -- Start of coordinate date range to analyze
    AnalysisDateTo DATETIME2 NOT NULL, -- End of coordinate date range to analyze
    JobStatus NVARCHAR(50) NOT NULL DEFAULT 'Pending', -- Pending, Running, Completed, Failed
    
    -- Job execution tracking
    StartedTimestamp DATETIME2 NULL, -- When job processing started
    CompletedTimestamp DATETIME2 NULL, -- When job processing completed
    
    -- Results summary
    CoordinatesProcessed INT NOT NULL DEFAULT 0, -- Number of coordinates analyzed
    FenceEventsCreated INT NOT NULL DEFAULT 0, -- Number of fence events generated
    
    -- Error handling
    ErrorMessage NVARCHAR(MAX) NULL, -- Error details if job failed
    
    -- Audit fields
    Created DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER NOT NULL,
    Changed DATETIME2 NULL,
    ChangedBy UNIQUEIDENTIFIER NULL,
    
    -- Indexes
    INDEX IX_GeofenceAnalysisJobs_VesselId (VesselId),
    INDEX IX_GeofenceAnalysisJobs_Status (JobStatus),
    INDEX IX_GeofenceAnalysisJobs_CreatedBy (CreatedBy),
    INDEX IX_GeofenceAnalysisJobs_DateRange (AnalysisDateFrom, AnalysisDateTo),
    
    -- Check constraints
    CHECK (AnalysisDateTo >= AnalysisDateFrom),
    CHECK (JobStatus IN ('Pending', 'Running', 'Completed', 'Failed'))
);