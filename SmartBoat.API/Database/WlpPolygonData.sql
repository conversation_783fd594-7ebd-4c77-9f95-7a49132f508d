-- WLP Polygon Data Table - Store parsed polygon data from templates
CREATE TABLE WlpPolygonData (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    TemplateId UNIQUEIDENTIFIER NOT NULL, -- Foreign key to WlpTemplates table
    ZoneId INT NOT NULL, -- Zone ID from the WLP file
    ZoneName NVARCHAR(255) NOT NULL, -- Zone name from WLP file
    ZoneDescription NVARCHAR(1000) NULL, -- Zone description from WLP file
    PolygonData NVARCHAR(MAX) NOT NULL, -- JSON serialized polygon coordinate data
    PointCount INT NOT NULL DEFAULT 0, -- Number of points in the polygon for quick reference
    
    -- Fast float fields for service layer queries
    MinLat FLOAT NOT NULL, -- Minimum latitude for fast queries
    MaxLat FLOAT NOT NULL, -- Maximum latitude for fast queries
    MinLng FLOAT NOT NULL, -- Minimum longitude for fast queries
    MaxLng FLOAT NOT NULL, -- Maximum longitude for fast queries
    
    -- Precise decimal bounding box for optimization
    BoundingBoxMinLat DECIMAL(10,7) NOT NULL, -- Bounding box for optimization
    BoundingBoxMaxLat DECIMAL(10,7) NOT NULL,
    BoundingBoxMinLng DECIMAL(11,7) NOT NULL,
    BoundingBoxMaxLng DECIMAL(11,7) NOT NULL,
    
    -- Audit fields
    Created DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER NOT NULL,
    
    -- Foreign key constraint
    FOREIGN KEY (TemplateId) REFERENCES WlpTemplates(Id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX IX_WlpPolygonData_TemplateId (TemplateId),
    INDEX IX_WlpPolygonData_ZoneId (ZoneId),
    INDEX IX_WlpPolygonData_ZoneName (ZoneName),
    INDEX IX_WlpPolygonData_BoundingBox (BoundingBoxMinLat, BoundingBoxMaxLat, BoundingBoxMinLng, BoundingBoxMaxLng),
    INDEX IX_WlpPolygonData_FastBounds (MinLat, MaxLat, MinLng, MaxLng),
    
    -- Unique constraint to prevent duplicate zones per template
    UNIQUE (TemplateId, ZoneId)
);