-- WLP Templates Table - Store uploaded WLP files as reusable templates
CREATE TABLE WlpTemplates (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(255) NOT NULL, -- User-defined template name
    Description NVARCHAR(MAX) NULL, -- Optional template description
    FileName NVARCHAR(255) NOT NULL, -- File name provided by user
    OriginalFilename NVARCHAR(255) NOT NULL, -- Original WLP file name
    UploadedBy UNIQUEIDENTIFIER NOT NULL, -- Foreign key to Users table
    UploadTimestamp DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    IsActive BIT NOT NULL DEFAULT 1, -- Active flag
    IsDeleted BIT NOT NULL DEFAULT 0, -- Soft delete flag
    FileSize BIGINT NOT NULL, -- File size in bytes
    ZoneCount INT NOT NULL DEFAULT 0, -- Number of zones parsed from file
    FileContentHash NVARCHAR(64) NOT NULL, -- SHA256 hash to detect duplicates
    
    -- Audit fields
    Created DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER NOT NULL,
    Changed DATETIME2 NULL,
    ChangedBy UNIQUEIDENTIFIER NULL,
    
    -- Indexes
    INDEX IX_WlpTemplates_UploadedBy (UploadedBy),
    INDEX IX_WlpTemplates_IsActive (IsActive),
    INDEX IX_WlpTemplates_IsDeleted (IsDeleted),
    INDEX IX_WlpTemplates_Hash (FileContentHash),
    INDEX IX_WlpTemplates_CreatedBy (CreatedBy)
);