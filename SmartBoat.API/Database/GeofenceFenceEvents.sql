CREATE TABLE GeofenceFenceEvents (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    JobId UNIQUEIDENTIFIER NOT NULL,
    VesselId UNIQUEIDENTIFIER NOT NULL,
    TemplateId UNIQUEIDENTIFIER NOT NULL,
    PolygonId UNIQUEIDENTIFIER NOT NULL,
    
    -- Event details
    EventType NVARCHAR(20) NOT NULL CHECK (EventType IN ('Entry', 'Exit')),
    EventTimestamp DATETIME2 NOT NULL,
    
    -- Coordinate information
    Latitude FLOAT NOT NULL,
    Longitude FLOAT NOT NULL,
    
    -- Context information
    PolygonName NVARCHAR(200) NOT NULL,
    TemplateName NVARCHAR(200) NOT NULL,
    
    -- Duration tracking (for exit events)
    DurationInside INT NULL, -- minutes spent inside the geofence
    
    -- Audit fields
    Created DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    
    -- Foreign keys
    CONSTRAINT FK_GeofenceFenceEvents_GeofenceAnalysisJobs 
        FOREIGN KEY (JobId) REFERENCES GeofenceAnalysisJobs(Id) ON DELETE CASCADE,
    CONSTRAINT FK_GeofenceFenceEvents_Vessels 
        FOREIGN KEY (VesselId) REFERENCES Vessels(Id),
    CONSTRAINT FK_GeofenceFenceEvents_WlpTemplates 
        FOREIGN KEY (TemplateId) REFERENCES WlpTemplates(Id),
    CONSTRAINT FK_GeofenceFenceEvents_WlpPolygonData 
        FOREIGN KEY (PolygonId) REFERENCES WlpPolygonData(Id)
);

-- Index for efficient querying by job
CREATE INDEX IX_GeofenceFenceEvents_JobId ON GeofenceFenceEvents(JobId);

-- Index for efficient querying by vessel and time
CREATE INDEX IX_GeofenceFenceEvents_VesselId_EventTimestamp ON GeofenceFenceEvents(VesselId, EventTimestamp);

-- Index for efficient querying by template
CREATE INDEX IX_GeofenceFenceEvents_TemplateId ON GeofenceFenceEvents(TemplateId);