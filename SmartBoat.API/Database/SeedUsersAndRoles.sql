-- ============================================================================
-- SmartBoat Users and Roles Seed Script
-- ============================================================================
-- This script creates sample roles, users, companies, and customers for development/testing
-- Based on 4-role model: Super Admin, Platform Admin, Customer Admin, Customer Employee

USE SmartBoat;
GO

PRINT 'Seeding SmartBoat users and roles...';

-- ============================================================================
-- 1. CREATE ROLES
-- ============================================================================

PRINT 'Creating roles...';

-- Clear existing roles if they exist (for development)
DELETE FROM Role WHERE Name IN ('Super Admin', 'Platform Admin', 'Customer Admin', 'Customer Employee');

-- Insert 4 core roles
INSERT INTO Role (Id, Name, Description, Created, Changed) VALUES
(NEWID(), 'Super Admin', 'Complete platform control and system administration. Full access to all platform functions and customer data.', GETUTCDATE(), NULL),
(NEWID(), 'Platform Admin', 'SmartBoat employees managing day-to-day operations, customer support, and platform monitoring.', GETUTCDATE(), NULL),
(NEWID(), 'Customer Admin', 'Company owners/fleet managers with full control over their company data, vessels, and employees.', GETUTCDATE(), NULL),
(NEWID(), 'Customer Employee', 'Operational users working with vessels day-to-day, limited to assigned vessels and tasks.', GETUTCDATE(), NULL);

-- Get role IDs for user creation
DECLARE @SuperAdminRoleId uniqueidentifier = (SELECT Id FROM Role WHERE Name = 'Super Admin');
DECLARE @PlatformAdminRoleId uniqueidentifier = (SELECT Id FROM Role WHERE Name = 'Platform Admin');
DECLARE @CustomerAdminRoleId uniqueidentifier = (SELECT Id FROM Role WHERE Name = 'Customer Admin');
DECLARE @CustomerEmployeeRoleId uniqueidentifier = (SELECT Id FROM Role WHERE Name = 'Customer Employee');

PRINT 'Roles created successfully.';

-- ============================================================================
-- 2. CREATE SAMPLE CUSTOMERS (Maritime Companies)
-- ============================================================================

PRINT 'Creating sample customers...';

-- Handle existing customers - get existing IDs or create new ones
DECLARE @Customer1Id uniqueidentifier;
DECLARE @Customer2Id uniqueidentifier;
DECLARE @Customer3Id uniqueidentifier;

-- Check if customers already exist and get their IDs, or create new ones
SELECT @Customer1Id = Id FROM Customer WHERE Name = 'Atlantic Maritime Solutions';
IF @Customer1Id IS NULL
BEGIN
    SET @Customer1Id = NEWID();
    INSERT INTO Customer (Id, Name, ContactPerson, Email, Phone, Status, LastActive, Created, Changed) VALUES
    (@Customer1Id, 'Atlantic Maritime Solutions', 'Captain James Morrison', '<EMAIL>', '******-0101', 'Active', GETUTCDATE(), GETUTCDATE(), NULL);
    PRINT 'Created customer: Atlantic Maritime Solutions';
END
ELSE
BEGIN
    PRINT 'Using existing customer: Atlantic Maritime Solutions';
END

SELECT @Customer2Id = Id FROM Customer WHERE Name = 'Pacific Charter Services';
IF @Customer2Id IS NULL
BEGIN
    SET @Customer2Id = NEWID();
    INSERT INTO Customer (Id, Name, ContactPerson, Email, Phone, Status, LastActive, Created, Changed) VALUES
    (@Customer2Id, 'Pacific Charter Services', 'Sarah Chen', '<EMAIL>', '******-0102', 'Active', GETUTCDATE(), GETUTCDATE(), NULL);
    PRINT 'Created customer: Pacific Charter Services';
END
ELSE
BEGIN
    PRINT 'Using existing customer: Pacific Charter Services';
END

SELECT @Customer3Id = Id FROM Customer WHERE Name = 'Nordic Fishing Fleet';
IF @Customer3Id IS NULL
BEGIN
    SET @Customer3Id = NEWID();
    INSERT INTO Customer (Id, Name, ContactPerson, Email, Phone, Status, LastActive, Created, Changed) VALUES
    (@Customer3Id, 'Nordic Fishing Fleet', 'Erik Andersen', '<EMAIL>', '******-0103', 'Active', GETUTCDATE(), GETUTCDATE(), NULL);
    PRINT 'Created customer: Nordic Fishing Fleet';
END
ELSE
BEGIN
    PRINT 'Using existing customer: Nordic Fishing Fleet';
END

PRINT 'Sample customers created.';

-- ============================================================================
-- 3. CREATE SAMPLE COMPANIES (Associated with Customers)
-- ============================================================================

PRINT 'Creating sample companies...';

-- Handle existing companies - get existing IDs or create new ones
DECLARE @Company1Id uniqueidentifier;
DECLARE @Company2Id uniqueidentifier;
DECLARE @Company3Id uniqueidentifier;

-- Check if companies already exist and get their IDs, or create new ones
SELECT @Company1Id = Id FROM Company WHERE Name = 'Atlantic Maritime Solutions - SmartBoat Demo';
IF @Company1Id IS NULL
BEGIN
    SET @Company1Id = NEWID();
    INSERT INTO Company (Id, Name, Location, Industry, Status, CustomerId, Created, Changed) VALUES
    (@Company1Id, 'Atlantic Maritime Solutions - SmartBoat Demo', 'Boston, MA', 'Commercial Shipping', 'Active', @Customer1Id, GETUTCDATE(), NULL);
    PRINT 'Created company: Atlantic Maritime Solutions - SmartBoat Demo';
END
ELSE
BEGIN
    PRINT 'Using existing company: Atlantic Maritime Solutions - SmartBoat Demo';
END

SELECT @Company2Id = Id FROM Company WHERE Name = 'Pacific Charter Services - SmartBoat Demo';
IF @Company2Id IS NULL
BEGIN
    SET @Company2Id = NEWID();
    INSERT INTO Company (Id, Name, Location, Industry, Status, CustomerId, Created, Changed) VALUES
    (@Company2Id, 'Pacific Charter Services - SmartBoat Demo', 'San Diego, CA', 'Charter Services', 'Active', @Customer2Id, GETUTCDATE(), NULL);
    PRINT 'Created company: Pacific Charter Services - SmartBoat Demo';
END
ELSE
BEGIN
    PRINT 'Using existing company: Pacific Charter Services - SmartBoat Demo';
END

SELECT @Company3Id = Id FROM Company WHERE Name = 'Nordic Fishing Fleet - SmartBoat Demo';
IF @Company3Id IS NULL
BEGIN
    SET @Company3Id = NEWID();
    INSERT INTO Company (Id, Name, Location, Industry, Status, CustomerId, Created, Changed) VALUES
    (@Company3Id, 'Nordic Fishing Fleet - SmartBoat Demo', 'Seattle, WA', 'Commercial Fishing', 'Active', @Customer3Id, GETUTCDATE(), NULL);
    PRINT 'Created company: Nordic Fishing Fleet - SmartBoat Demo';
END
ELSE
BEGIN
    PRINT 'Using existing company: Nordic Fishing Fleet - SmartBoat Demo';
END

PRINT 'Sample companies created.';

-- ============================================================================
-- 4. CREATE SAMPLE USERS
-- ============================================================================

PRINT 'Creating sample users...';

-- Clear existing test users only if they exist (safer for manual additions)
IF EXISTS (SELECT 1 FROM Users WHERE Email LIKE '%@smartboat.demo' OR Email LIKE '%@smartboat-demo.com')
BEGIN
    PRINT 'Cleaning existing demo users for re-seeding...';
    DELETE FROM Users WHERE Email LIKE '%@smartboat.demo' OR Email LIKE '%@smartboat-demo.com';
    PRINT 'Demo users cleaned successfully.';
END
ELSE
BEGIN
    PRINT 'No existing demo users found, proceeding with fresh user setup...';
END

-- Platform Users (SmartBoat Internal)
INSERT INTO Users (
    Id, Username, Email, PasswordHash, FirstName, LastName, RoleId, Role, Status,
    Created, Changed, LastLogin, TwoFactorEnabled, Avatar, Joined, Company, CompanyId,
    Department, Phone, PhoneNumber, Timezone, Language, Bio, IsDeleted
) VALUES
-- Super Admin
(NEWID(), 'superadmin', '<EMAIL>',
 '$2a$11$JtXA5xh5huAZhgsClf8SVuww/u9XM72n9Iqda9kxXYtugJTEdo6.G', -- "admin123"
 'Alex', 'Rodriguez', @SuperAdminRoleId, 'Super Admin', 'Active',
 GETUTCDATE(), NULL, GETUTCDATE(), 1, NULL, GETUTCDATE(), 'SmartBoat Inc', NULL,
 'Engineering', '******-0001', '******-0001', 'UTC', 'en', 'Platform founder and system administrator', 0),

-- Platform Admins
(NEWID(), 'support.manager', '<EMAIL>',
 '$2a$11$JtXA5xh5huAZhgsClf8SVuww/u9XM72n9Iqda9kxXYtugJTEdo6.G',
 'Maria', 'Santos', @PlatformAdminRoleId, 'Platform Admin', 'Active',
 GETUTCDATE(), NULL, GETUTCDATE(), 1, NULL, GETUTCDATE(), 'SmartBoat Inc', NULL,
 'Customer Success', '******-0002', '******-0002', 'EST', 'en', 'Customer success and platform operations manager', 0),

(NEWID(), 'tech.support', '<EMAIL>',
 '$2a$11$JtXA5xh5huAZhgsClf8SVuww/u9XM72n9Iqda9kxXYtugJTEdo6.G',
 'David', 'Kim', @PlatformAdminRoleId, 'Platform Admin', 'Active',
 GETUTCDATE(), NULL, GETUTCDATE(), 0, NULL, GETUTCDATE(), 'SmartBoat Inc', NULL,
 'Technical Support', '******-0003', '******-0003', 'PST', 'en', 'Technical support specialist', 0),

-- Atlantic Maritime Solutions Users
(NEWID(), 'j.morrison', '<EMAIL>',
 '$2a$11$JtXA5xh5huAZhgsClf8SVuww/u9XM72n9Iqda9kxXYtugJTEdo6.G',
 'James', 'Morrison', @CustomerAdminRoleId, 'Customer Admin', 'Active',
 GETUTCDATE(), NULL, GETUTCDATE(), 1, NULL, GETUTCDATE(), 'Atlantic Maritime Solutions', @Company1Id,
 'Fleet Management', '******-0101', '******-0101', 'EST', 'en', 'Fleet manager with 20+ years maritime experience', 0),

(NEWID(), 'r.thompson', '<EMAIL>',
 '$2a$11$JtXA5xh5huAZhgsClf8SVuww/u9XM72n9Iqda9kxXYtugJTEdo6.G',
 'Robert', 'Thompson', @CustomerEmployeeRoleId, 'Customer Employee', 'Active',
 GETUTCDATE(), NULL, GETUTCDATE(), 0, NULL, GETUTCDATE(), 'Atlantic Maritime Solutions', @Company1Id,
 'Vessel Operations', '******-0111', '******-0111', 'EST', 'en', 'Senior vessel operator and maintenance specialist', 0),

(NEWID(), 'l.martinez', '<EMAIL>',
 '$2a$11$JtXA5xh5huAZhgsClf8SVuww/u9XM72n9Iqda9kxXYtugJTEdo6.G',
 'Luis', 'Martinez', @CustomerEmployeeRoleId, 'Customer Employee', 'Active',
 GETUTCDATE(), NULL, GETUTCDATE(), 0, NULL, GETUTCDATE(), 'Atlantic Maritime Solutions', @Company1Id,
 'Vessel Operations', '******-0112', '******-0112', 'EST', 'en', 'Vessel operator and safety coordinator', 0),

-- Pacific Charter Services Users
(NEWID(), 's.chen', '<EMAIL>',
 '$2a$11$JtXA5xh5huAZhgsClf8SVuww/u9XM72n9Iqda9kxXYtugJTEdo6.G',
 'Sarah', 'Chen', @CustomerAdminRoleId, 'Customer Admin', 'Active',
 GETUTCDATE(), NULL, GETUTCDATE(), 1, NULL, GETUTCDATE(), 'Pacific Charter Services', @Company2Id,
 'Business Operations', '******-0102', '******-0102', 'PST', 'en', 'Charter business owner and operations manager', 0),

(NEWID(), 'm.johnson', '<EMAIL>',
 '$2a$11$JtXA5xh5huAZhgsClf8SVuww/u9XM72n9Iqda9kxXYtugJTEdo6.G',
 'Michael', 'Johnson', @CustomerEmployeeRoleId, 'Customer Employee', 'Active',
 GETUTCDATE(), NULL, GETUTCDATE(), 0, NULL, GETUTCDATE(), 'Pacific Charter Services', @Company2Id,
 'Charter Operations', '******-0121', '******-0121', 'PST', 'en', 'Charter boat captain and tour guide', 0),

(NEWID(), 'a.davis', '<EMAIL>',
 '$2a$11$JtXA5xh5huAZhgsClf8SVuww/u9XM72n9Iqda9kxXYtugJTEdo6.G',
 'Amanda', 'Davis', @CustomerEmployeeRoleId, 'Customer Employee', 'Active',
 GETUTCDATE(), NULL, GETUTCDATE(), 0, NULL, GETUTCDATE(), 'Pacific Charter Services', @Company2Id,
 'Charter Operations', '******-0122', '******-0122', 'PST', 'en', 'First mate and charter operations specialist', 0),

-- Nordic Fishing Fleet Users
(NEWID(), 'e.andersen', '<EMAIL>',
 '$2a$11$JtXA5xh5huAZhgsClf8SVuww/u9XM72n9Iqda9kxXYtugJTEdo6.G',
 'Erik', 'Andersen', @CustomerAdminRoleId, 'Customer Admin', 'Active',
 GETUTCDATE(), NULL, GETUTCDATE(), 1, NULL, GETUTCDATE(), 'Nordic Fishing Fleet', @Company3Id,
 'Fleet Management', '******-0103', '******-0103', 'PST', 'en', 'Fishing fleet owner with family maritime tradition', 0),

(NEWID(), 'o.hansen', '<EMAIL>',
 '$2a$11$JtXA5xh5huAZhgsClf8SVuww/u9XM72n9Iqda9kxXYtugJTEdo6.G',
 'Olaf', 'Hansen', @CustomerEmployeeRoleId, 'Customer Employee', 'Active',
 GETUTCDATE(), NULL, GETUTCDATE(), 0, NULL, GETUTCDATE(), 'Nordic Fishing Fleet', @Company3Id,
 'Fishing Operations', '******-0131', '******-0131', 'PST', 'en', 'Experienced fishing boat captain', 0),

(NEWID(), 'n.larsen', '<EMAIL>',
 '$2a$11$JtXA5xh5huAZhgsClf8SVuww/u9XM72n9Iqda9kxXYtugJTEdo6.G',
 'Nils', 'Larsen', @CustomerEmployeeRoleId, 'Customer Employee', 'Active',
 GETUTCDATE(), NULL, GETUTCDATE(), 0, NULL, GETUTCDATE(), 'Nordic Fishing Fleet', @Company3Id,
 'Fishing Operations', '******-0132', '******-0132', 'PST', 'en', 'Fishing vessel engineer and maintenance specialist', 0);

PRINT 'Sample users created.';

-- ============================================================================
-- 5. SUMMARY
-- ============================================================================

DECLARE @RoleCount int = (SELECT COUNT(*) FROM Role WHERE Name IN ('Super Admin', 'Platform Admin', 'Customer Admin', 'Customer Employee'));
DECLARE @UserCount int = (SELECT COUNT(*) FROM Users WHERE Email LIKE '%@smartboat.demo' OR Email LIKE '%@smartboat-demo.com');
DECLARE @CustomerCount int = (SELECT COUNT(*) FROM Customer WHERE Email LIKE '%@smartboat-demo.com');
DECLARE @CompanyCount int = (SELECT COUNT(*) FROM Company WHERE Name LIKE '%SmartBoat Demo%');

PRINT '============================================================================';
PRINT 'Users and Roles Seed Complete';
PRINT '============================================================================';
PRINT 'Roles Created: ' + CAST(@RoleCount AS nvarchar(10));
PRINT 'Users Created: ' + CAST(@UserCount AS nvarchar(10));
PRINT 'Customers Created: ' + CAST(@CustomerCount AS nvarchar(10));
PRINT 'Companies Created: ' + CAST(@CompanyCount AS nvarchar(10));
PRINT '';
PRINT 'Role Distribution:';
PRINT '- Super Admin: 1 user (platform owner)';
PRINT '- Platform Admin: 2 users (SmartBoat support team)';
PRINT '- Customer Admin: 3 users (company managers)';
PRINT '- Customer Employee: 6 users (operational staff)';
PRINT '';
PRINT 'Test Credentials (all users): password = "admin123"';
PRINT 'Demo companies represent different maritime business types:';
PRINT '- Atlantic Maritime: Commercial shipping';
PRINT '- Pacific Charter: Charter services';
PRINT '- Nordic Fishing: Commercial fishing';
PRINT '============================================================================';