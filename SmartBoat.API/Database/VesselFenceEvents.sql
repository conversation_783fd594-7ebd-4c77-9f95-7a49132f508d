-- VesselFenceEvents table
-- Tracks when vessels exit and enter geofence boundaries
-- Each record represents one complete cycle of leaving and returning to the fence

CREATE TABLE [VesselFenceEvents] (
    Id uniqueidentifier PRIMARY KEY DEFAULT NEWID(),
    VesselId uniqueidentifier NOT NULL,
    ExitTimestamp datetime2(7) NOT NULL,        -- When vessel left the fence
    ExitLat float NOT NULL,                     -- Exit coordinates
    ExitLng float NOT NULL,
    EntryTimestamp datetime2(7) NULL,           -- When vessel returned (null if still outside)
    EntryLat float NULL,                        -- Return coordinates  
    EntryLng float NULL,
    DurationMinutes int NULL,                   -- Auto-calculated when event completes
    Status nvarchar(20) NOT NULL,               -- 'Active' or 'Completed'
    Created datetime2(7) NOT NULL DEFAULT GETUTCDATE(),
    Changed datetime2(7) NULL,
    
    CONSTRAINT FK_VesselFenceEvents_Vessel FOREIGN KEY (VesselId) REFERENCES [Vessels](Id) ON DELETE CASCADE
);

-- Create index for common queries
CREATE INDEX IX_VesselFenceEvents_VesselId ON [VesselFenceEvents](VesselId);
CREATE INDEX IX_VesselFenceEvents_Status ON [VesselFenceEvents](Status);
CREATE INDEX IX_VesselFenceEvents_ExitTimestamp ON [VesselFenceEvents](ExitTimestamp);