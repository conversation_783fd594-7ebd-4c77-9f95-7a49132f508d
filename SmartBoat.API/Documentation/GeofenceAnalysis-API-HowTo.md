Geofence Analysis API – How To

Prerequisites

- API running locally: https://localhost:7001 (HTTP also on 5001)
- Valid JWT token with Authorization header: `Authorization: Bearer <token>`
- Self-signed cert in dev: pass `-k` to curl or configure client to ignore cert validation

Auth and Payload Shapes

- Geofence Analysis endpoints use raw JSON bodies (no envelope)
  - Examples: `/api/geofence-analysis/jobs/list`, `/api/geofence-analysis/jobs/create`, `/jobs/{id}`
- Most other controllers use the envelope: `{ "Header": { ... }, "Payload": { ... } }`
  - Examples: `/api/WlpTemplate/*`, `/api/vessel/*`
- The middleware injects `Header.UserId` automatically when a valid JWT is present, so an empty `Header` is fine in requests.

Environment Variables

- Set these to avoid repeating values:
  - `export API_BASE_URL=https://localhost:7001`
  - `export TOKEN='<your-jwt-token>'`

Quick cURL Examples

- List geofence analysis jobs (raw JSON):
  - `curl -k -H "Authorization: Bearer $TOKEN" -H "Content-Type: application/json" -d '{}' "$API_BASE_URL/api/geofence-analysis/jobs/list"`

- List WLP templates (enveloped):
  - `curl -k -H "Authorization: Bearer $TOKEN" -H "Content-Type: application/json" -d '{"Header":{},"Payload":{}}' "$API_BASE_URL/api/WlpTemplate/list"`

- List vessels (enveloped):
  - `curl -k -H "Authorization: Bearer $TOKEN" -H "Content-Type: application/json" -d '{"Header":{},"Payload":{"pageLimit":25,"pageOffset":0,"sortField":"name","sortOrder":"ASC"}}' "$API_BASE_URL/api/vessel/list"`

- Create geofence analysis job (raw JSON):
  - `curl -k -H "Authorization: Bearer $TOKEN" -H "Content-Type: application/json" -d '{"vesselId":"<VESSEL_ID>","templateIds":["<TEMPLATE_ID>"],"analysisDateFrom":"2024-01-01T00:00:00.000Z","analysisDateTo":"2024-01-31T23:59:59.999Z","name":"API Test Analysis Job"}' "$API_BASE_URL/api/geofence-analysis/jobs/create"`

- Get job by id:
  - `curl -k -H "Authorization: Bearer $TOKEN" "$API_BASE_URL/api/geofence-analysis/jobs/<JOB_ID>"`

- Cancel job:
  - `curl -k -H "Authorization: Bearer $TOKEN" -H "Content-Type: application/json" -d '{}' "$API_BASE_URL/api/geofence-analysis/jobs/<JOB_ID>/cancel"`

- Delete job:
  - `curl -k -X DELETE -H "Authorization: Bearer $TOKEN" "$API_BASE_URL/api/geofence-analysis/jobs/<JOB_ID>"`

- List fence events (raw JSON):
  - `curl -k -H "Authorization: Bearer $TOKEN" -H "Content-Type: application/json" -d '{}' "$API_BASE_URL/api/geofence-analysis/fence-events"`

Automated Test Scripts

- Shell cURL script: `test_geofence_curl.sh`
  - Usage: `./test_geofence_curl.sh <TOKEN>` or set `TOKEN` env var
  - What it does:
    - Lists analysis jobs (raw JSON)
    - Lists templates (enveloped) and picks the first template id
    - Lists vessels (enveloped) and picks the first vessel id
    - Lists fence events
    - Creates an analysis job if both a vessel and a template are available

- Node/axios script: `test_geofence_api.js`
  - Usage: `TOKEN=$TOKEN node test_geofence_api.js`
  - Uses `https.Agent({ rejectUnauthorized: false })` for local dev TLS
  - Uses enveloped payloads for `/api/WlpTemplate/*` and `/api/vessel/*`, and raw JSON for geofence endpoints

Response Shapes

- Geofence Analysis endpoints: raw JSON arrays or objects (example job list: `[{...}, {...}]`)
- Envelope endpoints: `{ "payload": <data>, "exception": null }`
  - `/api/WlpTemplate/list` → payload is an array of templates
  - `/api/vessel/list` → payload is an object with `data` (array) and `metadata`

Troubleshooting 500 Errors

- Verify DB is reachable and schema exists (tables for geofence jobs/templates/events):
  - See SQL files under `SmartBoat.API/Database/`: `GeofenceAnalysisJobs.sql`, `GeofenceJobTemplates.sql`, `GeofenceFenceEvents.sql`, etc.
  - Use `SmartBoat.API/Database/setup-database.sh` to set up in Dockerized SQL Server
  - Check connection string in `SmartBoat.API/appsettings.Development.json` (DefaultConnection)

- Check API logs: `SmartBoat.API/api.log` for stack traces

- Auth errors show as 401; a 500 typically indicates DB or unhandled service exception

Notes

- In dev, the frontend API client now automatically supports both enveloped and raw responses, so UI calls will work once the backend returns 200.

