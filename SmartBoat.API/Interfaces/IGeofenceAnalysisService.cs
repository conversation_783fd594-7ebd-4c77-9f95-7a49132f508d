using SmartBoat.API.Types;

namespace SmartBoat.API.Interfaces
{
    public interface IGeofenceAnalysisService
    {
        /// <summary>
        /// Create and start a new geofence analysis job
        /// </summary>
        Task<GeofenceAnalysisJobDto> CreateAnalysisJobAsync(CreateGeofenceAnalysisJobDto request, Guid userId);

        /// <summary>
        /// Get analysis job history for a user
        /// </summary>
        Task<List<GeofenceAnalysisJobDto>> GetAnalysisJobsAsync(GetGeofenceAnalysisJobsDto request, Guid userId);

        /// <summary>
        /// Get a specific analysis job by ID
        /// </summary>
        Task<GeofenceAnalysisJobDto?> GetAnalysisJobByIdAsync(Guid jobId, Guid userId);

        /// <summary>
        /// Check if an analysis already exists for the same vessel/templates/date range combination
        /// </summary>
        Task<List<GeofenceAnalysisJobDto>> FindExistingAnalysisAsync(Guid vesselId, List<Guid> templateIds, DateTime dateFrom, DateTime dateTo, Guid userId);

        /// <summary>
        /// Process coordinates for a vessel within a date range using selected templates
        /// </summary>
        Task ProcessCoordinatesAsync(Guid jobId, Guid vesselId, List<Guid> templateIds, DateTime dateFrom, DateTime dateTo, Guid userId);

        /// <summary>
        /// Get fence events created by a specific analysis job
        /// </summary>
        Task<List<GeofenceFenceEventDto>> GetJobFenceEventsAsync(Guid jobId, Guid userId);

        /// <summary>
        /// Get fence events with filtering and pagination
        /// </summary>
        Task<List<GeofenceFenceEventDto>> GetFenceEventsAsync(GetFenceEventsDto request, Guid userId);

        /// <summary>
        /// Get fence event summary for a job or vessel
        /// </summary>
        Task<FenceEventSummaryDto> GetFenceEventSummaryAsync(Guid? jobId, Guid? vesselId, DateTime? fromDate, DateTime? toDate, Guid userId);

        /// <summary>
        /// Cancel a pending or running analysis job
        /// </summary>
        Task<bool> CancelAnalysisJobAsync(Guid jobId, Guid userId);

        /// <summary>
        /// Delete an analysis job and its results
        /// </summary>
        Task<bool> DeleteAnalysisJobAsync(Guid jobId, Guid userId);
    }
}