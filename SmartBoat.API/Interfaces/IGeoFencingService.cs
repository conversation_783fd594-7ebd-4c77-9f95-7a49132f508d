using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Interfaces
{
    public interface IGeoFencingService
    {
        /// <summary>
        /// Checks vessel position against geo-fence and manages fence events
        /// </summary>
        Task CheckVesselPositionAsync(Guid vesselId, float lat, float lng, DateTime timestamp);
        
        /// <summary>
        /// Get fence events for a specific vessel
        /// </summary>
        Task<List<VesselFenceEventDto>> GetVesselFenceEventsAsync(GetVesselFenceEventsDto request);
        
        /// <summary>
        /// Get all currently active fence events (vessels outside fence)
        /// </summary>
        Task<List<VesselFenceEventDto>> GetActiveFenceEventsAsync();
        
        /// <summary>
        /// Check if coordinates are inside the geo-fence
        /// </summary>
        bool IsInsideGeoFence(float lat, float lng);
        
        /// <summary>
        /// Calculate distance between two coordinates in kilometers
        /// </summary>
        double CalculateDistanceKm(float lat1, float lng1, float lat2, float lng2);
        
        /// <summary>
        /// Get the name of the geofence zone containing the specified coordinates
        /// </summary>
        string GetGeofenceZoneName(float lat, float lng);
    }
}