using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SmartBoat.API.Types;

namespace SmartBoat.API.Interfaces
{
    public interface IVesselPathPointService
    {
        /// <summary>
        /// Create a single vessel path point
        /// </summary>
        /// <param name="pathPoint">The path point to create</param>
        /// <param name="userId">User ID for audit purposes</param>
        /// <returns>The ID of the created path point</returns>
        Task<string> CreatePathPointAsync(VesselPathPoint pathPoint, Guid userId);

        /// <summary>
        /// Create multiple vessel path points in bulk for performance
        /// </summary>
        /// <param name="pathPoints">List of path points to create</param>
        /// <param name="userId">User ID for audit purposes</param>
        /// <returns>List of created path point IDs</returns>
        Task<List<string>> CreatePathPointsBulkAsync(List<VesselPathPoint> pathPoints, Guid userId);

        /// <summary>
        /// Get vessel path points by vessel ID with optional date filtering
        /// </summary>
        /// <param name="vesselId">The vessel ID</param>
        /// <param name="startDate">Optional start date filter</param>
        /// <param name="endDate">Optional end date filter</param>
        /// <returns>List of vessel path points</returns>
        Task<List<VesselPathPoint>> GetPathPointsByVesselAsync(Guid vesselId, DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// Parse coordinate string from CSV data into latitude and longitude
        /// </summary>
        /// <param name="coordinateString">Coordinate string in various formats</param>
        /// <returns>Tuple of (latitude, longitude) or null if parsing fails</returns>
        (float? lat, float? lng)? ParseCoordinates(string coordinateString);

        /// <summary>
        /// Validate if coordinates are within valid ranges
        /// </summary>
        /// <param name="lat">Latitude</param>
        /// <param name="lng">Longitude</param>
        /// <returns>True if coordinates are valid</returns>
        bool ValidateCoordinates(float? lat, float? lng);
    }
}