using SmartBoat.API.Types;

namespace SmartBoat.API.Interfaces
{
    public interface IPolygonGeofenceService
    {
        bool IsPointInPolygon(double latitude, double longitude, List<GeofencePoint> polygonPoints);
        GeofencePolygon? FindContainingPolygon(double latitude, double longitude, List<GeofencePolygon> polygons);
        double CalculateMinDistanceToPolygon(double latitude, double longitude, List<GeofencePoint> polygonPoints);
    }
}