using SmartBoat.API.Interfaces;
using SmartBoat.API.Types;
using Nbg.NetCore.DatabaseService;
using System.Text.Json;

namespace SmartBoat.API.Implementations.GeofenceAnalysisService
{
    public partial class GeofenceAnalysisService
    {
        /// <summary>
        /// Process coordinates with fence event tracking (entry/exit detection)
        /// </summary>
        private async Task<int> ProcessCoordinatesWithFenceEventsAsync(
            Guid jobId, 
            List<VesselCoordinate> coordinates, 
            List<GeofencePolygonWithMetadata> geofencePolygons)
        {
            var eventsCreated = 0;
            var vesselPolygonStates = new Dictionary<Guid, bool>(); // PolygonId -> IsInside

            // Initialize all polygons as outside
            foreach (var polygon in geofencePolygons)
            {
                vesselPolygonStates[polygon.Id] = false;
            }

            // Sort coordinates by timestamp to ensure proper entry/exit detection
            var sortedCoordinates = coordinates.OrderBy(c => c.Timestamp).ToList();

            foreach (var coordinate in sortedCoordinates)
            {
                foreach (var polygon in geofencePolygons)
                {
                    var wasInside = vesselPolygonStates[polygon.Id];
                    var isNowInside = IsPointInPolygon(coordinate, polygon);

                    // Entry event
                    if (!wasInside && isNowInside)
                    {
                        await CreateFenceEventAsync(
                            jobId, 
                            coordinate, 
                            polygon, 
                            FenceEventType.Entry, 
                            null);
                        eventsCreated++;
                    }
                    // Exit event
                    else if (wasInside && !isNowInside)
                    {
                        // Calculate duration inside if we have the entry timestamp
                        var entryEvent = await GetLastEntryEventAsync(jobId, polygon.Id);
                        int? durationInside = null;
                        if (entryEvent != null)
                        {
                            var duration = coordinate.Timestamp - entryEvent.EventTimestamp;
                            durationInside = (int)duration.TotalMinutes;
                        }

                        await CreateFenceEventAsync(
                            jobId, 
                            coordinate, 
                            polygon, 
                            FenceEventType.Exit, 
                            durationInside);
                        eventsCreated++;
                    }

                    // Update state
                    vesselPolygonStates[polygon.Id] = isNowInside;
                }
            }

            return eventsCreated;
        }

        /// <summary>
        /// Create a fence event record
        /// </summary>
        private async Task CreateFenceEventAsync(
            Guid jobId,
            VesselCoordinate coordinate,
            GeofencePolygonWithMetadata polygon,
            FenceEventType eventType,
            int? durationInside)
        {
            try
            {
                var fenceEvent = new GeofenceFenceEvent
                {
                    Id = Guid.NewGuid(),
                    JobId = jobId,
                    VesselId = coordinate.VesselId,
                    TemplateId = polygon.TemplateId,
                    PolygonId = polygon.Id,
                    EventType = eventType.ToString(),
                    EventTimestamp = coordinate.Timestamp,
                    Latitude = coordinate.Latitude,
                    Longitude = coordinate.Longitude,
                    PolygonName = polygon.Name,
                    TemplateName = polygon.TemplateName,
                    DurationInside = durationInside,
                    Created = DateTime.UtcNow
                };

                await _databaseService.InsertAsync<GeofenceFenceEvent>(fenceEvent);

                _logger.LogDebug("Created {EventType} fence event for polygon {PolygonName} at {Timestamp}", 
                    eventType, polygon.Name, coordinate.Timestamp);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating fence event for job {JobId}, polygon {PolygonId}", 
                    jobId, polygon.Id);
                throw;
            }
        }

        /// <summary>
        /// Get the last entry event for a specific polygon to calculate duration
        /// </summary>
        private async Task<GeofenceFenceEvent?> GetLastEntryEventAsync(Guid jobId, Guid polygonId)
        {
            try
            {
                var events = await _databaseService.SelectAsync<GeofenceFenceEvent>(new
                {
                    JobId = jobId,
                    PolygonId = polygonId,
                    EventType = "Entry"
                });

                return events?.OrderByDescending(e => e.EventTimestamp).FirstOrDefault();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting last entry event for polygon {PolygonId}", polygonId);
                return null;
            }
        }

        /// <summary>
        /// Get fence events for a specific job
        /// </summary>
        public async Task<List<GeofenceFenceEventDto>> GetJobFenceEventsAsync(Guid jobId, Guid userId)
        {
            try
            {
                // Verify user has access to the job
                var jobs = await _databaseService.SelectAsync<GeofenceAnalysisJob>(new
                {
                    Id = jobId,
                    CreatedBy = userId
                });

                if (!jobs?.Any() == true)
                {
                    throw new UnauthorizedAccessException("Analysis job not found or access denied");
                }

                // Get fence events with additional metadata
                var events = await _databaseService.SelectAsync<GeofenceFenceEvent>(new { JobId = jobId });
                
                if (!events?.Any() == true)
                {
                    return new List<GeofenceFenceEventDto>();
                }

                // Convert to DTOs with formatted duration
                var eventDtos = events.Select(e => new GeofenceFenceEventDto
                {
                    Id = e.Id,
                    JobId = e.JobId,
                    VesselId = e.VesselId,
                    VesselName = "", // Would need to join with Vessels table
                    TemplateId = e.TemplateId,
                    TemplateName = e.TemplateName,
                    PolygonId = e.PolygonId,
                    PolygonName = e.PolygonName,
                    EventType = e.EventType,
                    EventTimestamp = e.EventTimestamp,
                    Latitude = e.Latitude,
                    Longitude = e.Longitude,
                    DurationInside = e.DurationInside,
                    DurationFormatted = FormatDuration(e.DurationInside),
                    Created = e.Created
                }).OrderBy(e => e.EventTimestamp).ToList();

                return eventDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting fence events for job {JobId}", jobId);
                throw;
            }
        }

        /// <summary>
        /// Get fence events with filtering
        /// </summary>
        public async Task<List<GeofenceFenceEventDto>> GetFenceEventsAsync(GetFenceEventsDto request, Guid userId)
        {
            try
            {
                // Build dynamic filter based on request parameters
                var filter = new Dictionary<string, object>();

                if (request.JobId.HasValue)
                {
                    // Verify user has access to the job
                    var jobs = await _databaseService.SelectAsync<GeofenceAnalysisJob>(new
                    {
                        Id = request.JobId.Value,
                        CreatedBy = userId
                    });

                    if (!jobs?.Any() == true)
                    {
                        return new List<GeofenceFenceEventDto>();
                    }

                    filter.Add("JobId", request.JobId.Value);
                }

                if (request.VesselId.HasValue)
                {
                    filter.Add("VesselId", request.VesselId.Value);
                }

                if (request.TemplateId.HasValue)
                {
                    filter.Add("TemplateId", request.TemplateId.Value);
                }

                if (!string.IsNullOrEmpty(request.EventType))
                {
                    filter.Add("EventType", request.EventType);
                }

                // For date range filtering, we'd need a more complex query
                // This is a simplified implementation
                var events = await _databaseService.SelectAsync<GeofenceFenceEvent>(filter);

                if (!events?.Any() == true)
                {
                    return new List<GeofenceFenceEventDto>();
                }

                // Apply date filtering in memory (in a real system, this should be done in SQL)
                var filteredEvents = events.AsEnumerable();

                if (request.FromDate.HasValue)
                {
                    filteredEvents = filteredEvents.Where(e => e.EventTimestamp >= request.FromDate.Value);
                }

                if (request.ToDate.HasValue)
                {
                    filteredEvents = filteredEvents.Where(e => e.EventTimestamp <= request.ToDate.Value);
                }

                // Apply search term filtering
                if (!string.IsNullOrEmpty(request.SearchTerm))
                {
                    var searchTerm = request.SearchTerm.ToLowerInvariant();
                    filteredEvents = filteredEvents.Where(e => 
                        e.PolygonName.ToLowerInvariant().Contains(searchTerm) ||
                        e.TemplateName.ToLowerInvariant().Contains(searchTerm));
                }

                // Apply pagination
                var sortedEvents = filteredEvents.OrderBy(e => e.EventTimestamp).AsEnumerable();
                
                if (request.PageOffset.HasValue && request.PageOffset > 0)
                {
                    sortedEvents = sortedEvents.Skip(request.PageOffset.Value);
                }

                if (request.PageLimit.HasValue && request.PageLimit > 0)
                {
                    sortedEvents = sortedEvents.Take(request.PageLimit.Value);
                }

                // Convert to DTOs
                var eventDtos = sortedEvents.Select(e => new GeofenceFenceEventDto
                {
                    Id = e.Id,
                    JobId = e.JobId,
                    VesselId = e.VesselId,
                    VesselName = "",
                    TemplateId = e.TemplateId,
                    TemplateName = e.TemplateName,
                    PolygonId = e.PolygonId,
                    PolygonName = e.PolygonName,
                    EventType = e.EventType,
                    EventTimestamp = e.EventTimestamp,
                    Latitude = e.Latitude,
                    Longitude = e.Longitude,
                    DurationInside = e.DurationInside,
                    DurationFormatted = FormatDuration(e.DurationInside),
                    Created = e.Created
                }).ToList();

                return eventDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting fence events with filters");
                throw;
            }
        }

        /// <summary>
        /// Get fence event summary statistics
        /// </summary>
        public async Task<FenceEventSummaryDto> GetFenceEventSummaryAsync(
            Guid? jobId, 
            Guid? vesselId, 
            DateTime? fromDate, 
            DateTime? toDate, 
            Guid userId)
        {
            try
            {
                // Build filter
                var filter = new Dictionary<string, object>();

                if (jobId.HasValue)
                {
                    // Verify user has access to the job
                    var jobs = await _databaseService.SelectAsync<GeofenceAnalysisJob>(new
                    {
                        Id = jobId.Value,
                        CreatedBy = userId
                    });

                    if (!jobs?.Any() == true)
                    {
                        return new FenceEventSummaryDto();
                    }

                    filter.Add("JobId", jobId.Value);
                }

                if (vesselId.HasValue)
                {
                    filter.Add("VesselId", vesselId.Value);
                }

                var events = await _databaseService.SelectAsync<GeofenceFenceEvent>(filter);

                if (!events?.Any() == true)
                {
                    return new FenceEventSummaryDto();
                }

                // Apply date filtering
                var filteredEvents = events.AsEnumerable();
                
                if (fromDate.HasValue)
                {
                    filteredEvents = filteredEvents.Where(e => e.EventTimestamp >= fromDate.Value);
                }

                if (toDate.HasValue)
                {
                    filteredEvents = filteredEvents.Where(e => e.EventTimestamp <= toDate.Value);
                }

                var eventList = filteredEvents.ToList();

                // Calculate summary statistics
                var summary = new FenceEventSummaryDto
                {
                    TotalEvents = eventList.Count,
                    EntryEvents = eventList.Count(e => e.EventType == "Entry"),
                    ExitEvents = eventList.Count(e => e.EventType == "Exit"),
                    UniquePolygonsEntered = eventList.Where(e => e.EventType == "Entry")
                        .Select(e => e.PolygonId).Distinct().Count(),
                    FirstEvent = eventList.Any() ? eventList.Min(e => e.EventTimestamp) : null,
                    LastEvent = eventList.Any() ? eventList.Max(e => e.EventTimestamp) : null
                };

                // Calculate time statistics for exit events (they have duration data)
                var exitEvents = eventList.Where(e => e.EventType == "Exit" && e.DurationInside.HasValue).ToList();
                if (exitEvents.Any())
                {
                    summary.TotalTimeInside = exitEvents.Sum(e => e.DurationInside ?? 0);
                    summary.AverageTimeInside = exitEvents.Average(e => e.DurationInside ?? 0);
                }

                // Calculate polygon-specific summaries
                summary.PolygonSummaries = eventList
                    .GroupBy(e => new { e.PolygonId, e.PolygonName, e.TemplateName })
                    .Select(g => new PolygonEventSummaryDto
                    {
                        PolygonId = g.Key.PolygonId,
                        PolygonName = g.Key.PolygonName,
                        TemplateName = g.Key.TemplateName,
                        EntryCount = g.Count(e => e.EventType == "Entry"),
                        ExitCount = g.Count(e => e.EventType == "Exit"),
                        TotalTimeInside = g.Where(e => e.EventType == "Exit" && e.DurationInside.HasValue)
                            .Sum(e => e.DurationInside ?? 0),
                        AverageTimeInside = g.Where(e => e.EventType == "Exit" && e.DurationInside.HasValue)
                            .Any() ? g.Where(e => e.EventType == "Exit" && e.DurationInside.HasValue)
                            .Average(e => e.DurationInside ?? 0) : 0,
                        FirstEntry = g.Where(e => e.EventType == "Entry").Any() ? 
                            g.Where(e => e.EventType == "Entry").Min(e => e.EventTimestamp) : null,
                        LastExit = g.Where(e => e.EventType == "Exit").Any() ? 
                            g.Where(e => e.EventType == "Exit").Max(e => e.EventTimestamp) : null
                    })
                    .OrderByDescending(p => p.EntryCount)
                    .ToList();

                return summary;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting fence event summary");
                throw;
            }
        }

        /// <summary>
        /// Format duration in minutes to human-readable string
        /// </summary>
        private string? FormatDuration(int? durationMinutes)
        {
            if (!durationMinutes.HasValue || durationMinutes.Value <= 0)
                return null;

            var totalMinutes = durationMinutes.Value;
            var hours = totalMinutes / 60;
            var minutes = totalMinutes % 60;

            if (hours > 0)
            {
                return minutes > 0 ? $"{hours}h {minutes}m" : $"{hours}h";
            }
            else
            {
                return $"{minutes}m";
            }
        }
    }

    /// <summary>
    /// Extended polygon class with metadata for fence event tracking
    /// </summary>
    public class GeofencePolygonWithMetadata : GeofencePolygon
    {
        public Guid Id { get; set; }
        public new Guid TemplateId { get; set; }
        public string TemplateName { get; set; } = string.Empty;
    }
}