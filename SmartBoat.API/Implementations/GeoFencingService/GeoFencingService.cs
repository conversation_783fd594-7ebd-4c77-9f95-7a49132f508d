using SmartBoat.API.Interfaces;
using SmartBoat.API.Types;
using SmartBoat.API.Services;
using Nbg.NetCore.DatabaseService;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Implementations.GeoFencingService
{
    public class GeoFencingService : IGeoFencingService
    {
        private readonly IDatabaseService _databaseService;
        private readonly ILogger<GeoFencingService> _logger;
        private readonly IWlpGeofenceParser _wlpParser;
        private readonly IPolygonGeofenceService _polygonService;
        private readonly IWebHostEnvironment _environment;
        private List<GeofencePolygon> _geofencePolygons = new List<GeofencePolygon>();

        public GeoFencingService(
            IDatabaseService databaseService, 
            ILogger<GeoFencingService> logger,
            IWlpGeofenceParser wlpParser,
            IPolygonGeofenceService polygonService,
            IWebHostEnvironment environment)
        {
            _databaseService = databaseService;
            _databaseService.SetConnection("DefaultConnection");
            _logger = logger;
            _wlpParser = wlpParser;
            _polygonService = polygonService;
            _environment = environment;
            
            _ = InitializeGeofencesAsync();
        }

        private async Task InitializeGeofencesAsync()
        {
            try
            {
                var wlpFilePath = Path.Combine(
                    _environment.ContentRootPath, 
                    "Implementations", 
                    "GeoFencingService", 
                    "geofenceData.wlp");

                var wlpData = await _wlpParser.ParseWlpFileAsync(wlpFilePath);
                if (wlpData != null)
                {
                    _geofencePolygons = _wlpParser.ConvertToGeofencePolygons(wlpData);
                    _logger.LogInformation("Successfully loaded {PolygonCount} geofence polygons from WLP file", _geofencePolygons.Count);
                }
                else
                {
                    _logger.LogError("Failed to load WLP geofence data from {FilePath}", wlpFilePath);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing geofences from WLP file");
            }
        }

        public async Task CheckVesselPositionAsync(Guid vesselId, float lat, float lng, DateTime timestamp)
        {
            try
            {
                bool isInsideFence = IsInsideGeoFence(lat, lng);
                
                // Get the most recent active fence event for this vessel
                var activeEvents = await _databaseService.SelectAsync<VesselFenceEvent>(new { VesselId = vesselId, Status = "Active" });
                var activeEvent = activeEvents?.OrderByDescending(e => e.ExitTimestamp).FirstOrDefault();

                if (!isInsideFence && activeEvent == null)
                {
                    // Vessel just exited the fence - create new event
                    var newEvent = new VesselFenceEvent
                    {
                        Id = Guid.NewGuid(),
                        VesselId = vesselId,
                        ExitTimestamp = timestamp,
                        ExitLat = lat,
                        ExitLng = lng,
                        Status = "Active",
                        Created = DateTime.UtcNow
                    };

                    await _databaseService.InsertAsync(newEvent);
                    
                    _logger.LogInformation($"Vessel {vesselId} exited geo-fence at {timestamp}");
                }
                else if (isInsideFence && activeEvent != null)
                {
                    // Vessel returned to fence - complete the event
                    activeEvent.EntryTimestamp = timestamp;
                    activeEvent.EntryLat = lat;
                    activeEvent.EntryLng = lng;
                    activeEvent.Status = "Completed";
                    activeEvent.Changed = DateTime.UtcNow;
                    
                    // Calculate duration in minutes
                    var duration = (timestamp - activeEvent.ExitTimestamp).TotalMinutes;
                    activeEvent.DurationMinutes = (int)Math.Round(duration);

                    await _databaseService.UpdateAsync<VesselFenceEvent>(activeEvent, new { Id = activeEvent.Id });
                    
                    _logger.LogInformation($"Vessel {vesselId} returned to geo-fence at {timestamp}, duration: {activeEvent.DurationMinutes} minutes");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking vessel position for vessel {vesselId}");
            }
        }

        public async Task<List<VesselFenceEventDto>> GetVesselFenceEventsAsync(GetVesselFenceEventsDto request)
        {
            // Build filter object for database query
            var filter = new { VesselId = request.VesselId };
            
            // Get all events for the vessel (database service will handle basic filtering)
            var events = await _databaseService.SelectAsync<VesselFenceEvent>(filter);
            
            if (events == null || !events.Any())
            {
                return new List<VesselFenceEventDto>();
            }

            // Apply additional filters in memory (can be optimized later with SQL queries)
            var filteredEvents = events.AsEnumerable();

            if (!string.IsNullOrEmpty(request.Status))
            {
                filteredEvents = filteredEvents.Where(e => e.Status == request.Status);
            }

            if (request.FromDate.HasValue)
            {
                filteredEvents = filteredEvents.Where(e => e.ExitTimestamp >= request.FromDate.Value);
            }
            
            if (request.ToDate.HasValue)
            {
                filteredEvents = filteredEvents.Where(e => e.ExitTimestamp <= request.ToDate.Value);
            }

            // Apply ordering and pagination
            var paginatedEvents = filteredEvents
                .OrderByDescending(e => e.ExitTimestamp)
                .Skip(request.PageOffset ?? 0)
                .Take(request.PageLimit ?? 25);

            // Get vessel name for each event
            var result = new List<VesselFenceEventDto>();
            foreach (var evt in paginatedEvents)
            {
                string vesselName = "";
                try
                {
                    var vessel = await _databaseService.SelectByIdAsync<Vessel, Guid>(evt.VesselId);
                    vesselName = vessel?.Name ?? "";
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, $"Failed to get vessel name for vessel {evt.VesselId}");
                }

                result.Add(new VesselFenceEventDto
                {
                    Id = evt.Id,
                    VesselId = evt.VesselId,
                    ExitTimestamp = evt.ExitTimestamp,
                    ExitLat = evt.ExitLat,
                    ExitLng = evt.ExitLng,
                    EntryTimestamp = evt.EntryTimestamp,
                    EntryLat = evt.EntryLat,
                    EntryLng = evt.EntryLng,
                    DurationMinutes = evt.DurationMinutes,
                    Status = evt.Status,
                    Created = evt.Created,
                    Changed = evt.Changed,
                    VesselName = vesselName
                });
            }

            return result;
        }

        public async Task<List<VesselFenceEventDto>> GetActiveFenceEventsAsync()
        {
            // Get all active events
            var activeEvents = await _databaseService.SelectAsync<VesselFenceEvent>(new { Status = "Active" });
            
            if (activeEvents == null || !activeEvents.Any())
            {
                return new List<VesselFenceEventDto>();
            }

            var result = new List<VesselFenceEventDto>();
            foreach (var evt in activeEvents.OrderByDescending(e => e.ExitTimestamp))
            {
                string vesselName = "";
                try
                {
                    var vessel = await _databaseService.SelectByIdAsync<Vessel, Guid>(evt.VesselId);
                    vesselName = vessel?.Name ?? "";
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, $"Failed to get vessel name for vessel {evt.VesselId}");
                }

                result.Add(new VesselFenceEventDto
                {
                    Id = evt.Id,
                    VesselId = evt.VesselId,
                    ExitTimestamp = evt.ExitTimestamp,
                    ExitLat = evt.ExitLat,
                    ExitLng = evt.ExitLng,
                    Status = evt.Status,
                    Created = evt.Created,
                    VesselName = vesselName
                });
            }

            return result;
        }

        public bool IsInsideGeoFence(float lat, float lng)
        {
            if (_geofencePolygons.Count == 0)
            {
                _logger.LogWarning("No geofence polygons loaded, defaulting to false");
                return false;
            }

            var containingPolygon = _polygonService.FindContainingPolygon(lat, lng, _geofencePolygons);
            return containingPolygon != null;
        }

        public string GetGeofenceZoneName(float lat, float lng)
        {
            if (_geofencePolygons.Count == 0)
            {
                return "Unknown";
            }

            var containingPolygon = _polygonService.FindContainingPolygon(lat, lng, _geofencePolygons);
            return containingPolygon?.Name ?? "Outside all zones";
        }

        public double CalculateDistanceKm(float lat1, float lng1, float lat2, float lng2)
        {
            const double earthRadiusKm = 6371.0;

            // Convert degrees to radians
            double lat1Rad = Math.PI * lat1 / 180.0;
            double lng1Rad = Math.PI * lng1 / 180.0;
            double lat2Rad = Math.PI * lat2 / 180.0;
            double lng2Rad = Math.PI * lng2 / 180.0;

            // Haversine formula
            double dLat = lat2Rad - lat1Rad;
            double dLng = lng2Rad - lng1Rad;
            
            double a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                      Math.Cos(lat1Rad) * Math.Cos(lat2Rad) *
                      Math.Sin(dLng / 2) * Math.Sin(dLng / 2);
            
            double c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
            
            return earthRadiusKm * c;
        }
    }
}