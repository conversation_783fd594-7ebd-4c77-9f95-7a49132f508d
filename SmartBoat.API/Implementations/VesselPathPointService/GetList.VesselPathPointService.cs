using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class VesselPathPointService
    {
        public async Task<List<VesselPathPoint>> GetPathPointsByVesselAsync(Guid vesselId, DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                _logger.LogDebug("Retrieving path points for vessel {VesselId} from {StartDate} to {EndDate}", 
                    vesselId, startDate, endDate);

                // Get all path points for the vessel
                var pathPoints = await _databaseService.SelectAsync<VesselPathPoint>(new { VesselId = vesselId });
                
                if (pathPoints == null || !pathPoints.Any())
                {
                    _logger.LogDebug("No path points found for vessel {VesselId}", vesselId);
                    return new List<VesselPathPoint>();
                }

                var filteredPoints = pathPoints.AsEnumerable();

                // Apply date filtering if provided
                if (startDate.HasValue)
                {
                    filteredPoints = filteredPoints.Where(p => 
                        p.Timestamp.HasValue && p.Timestamp.Value >= startDate.Value);
                }

                if (endDate.HasValue)
                {
                    filteredPoints = filteredPoints.Where(p => 
                        p.Timestamp.HasValue && p.Timestamp.Value <= endDate.Value);
                }

                // Order by timestamp for chronological path reconstruction
                var result = filteredPoints
                    .Where(p => p.Timestamp.HasValue) // Only include points with valid timestamps
                    .OrderBy(p => p.Timestamp.Value)
                    .ToList();

                _logger.LogDebug("Retrieved {Count} path points for vessel {VesselId}", result.Count, vesselId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving path points for vessel {VesselId}", vesselId);
                throw;
            }
        }
    }
}