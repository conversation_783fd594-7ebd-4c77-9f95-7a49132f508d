using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class VesselPathPointService
    {
        public async Task<string> CreatePathPointAsync(VesselPathPoint pathPoint, Guid userId)
        {
            try
            {
                // Set default values
                pathPoint.Id = pathPoint.Id ?? Guid.NewGuid();
                pathPoint.Created = DateTime.UtcNow;
                pathPoint.Changed = DateTime.UtcNow;

                // Validate coordinates
                if (!ValidateCoordinates(pathPoint.Lat, pathPoint.Lng))
                {
                    _logger.LogWarning("Invalid coordinates provided for vessel path point: Lat={Lat}, Lng={Lng}", 
                        pathPoint.Lat, pathPoint.Lng);
                    throw new ArgumentException("Invalid coordinates provided");
                }

                // Check for duplicate (same vessel + timestamp within 1 minute)
                var existingPoints = await _databaseService.SelectAsync<VesselPathPoint>(new 
                { 
                    VesselId = pathPoint.VesselId 
                });

                if (existingPoints?.Any() == true)
                {
                    var duplicate = existingPoints.FirstOrDefault(p => 
                        p.Timestamp.HasValue && pathPoint.Timestamp.HasValue &&
                        Math.Abs((p.Timestamp.Value - pathPoint.Timestamp.Value).TotalMinutes) < 1);
                    
                    if (duplicate != null)
                    {
                        _logger.LogDebug("Duplicate path point detected for vessel {VesselId} at timestamp {Timestamp}. Skipping.", 
                            pathPoint.VesselId, pathPoint.Timestamp);
                        return duplicate.Id.ToString();
                    }
                }

                // Insert the path point
                var insertResult = await _databaseService.InsertAsync(pathPoint);
                
                _logger.LogDebug("Created vessel path point {Id} for vessel {VesselId} at coordinates ({Lat}, {Lng})", 
                    pathPoint.Id, pathPoint.VesselId, pathPoint.Lat, pathPoint.Lng);

                return pathPoint.Id.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating vessel path point for vessel {VesselId}", pathPoint.VesselId);
                throw;
            }
        }

        public async Task<List<string>> CreatePathPointsBulkAsync(List<VesselPathPoint> pathPoints, Guid userId)
        {
            try
            {
                if (pathPoints == null || !pathPoints.Any())
                {
                    return new List<string>();
                }

                var createdIds = new List<string>();
                var validPathPoints = new List<VesselPathPoint>();

                // Validate and prepare all path points
                foreach (var pathPoint in pathPoints)
                {
                    // Set default values
                    pathPoint.Id = pathPoint.Id ?? Guid.NewGuid();
                    pathPoint.Created = DateTime.UtcNow;
                    pathPoint.Changed = DateTime.UtcNow;

                    // Validate coordinates
                    if (!ValidateCoordinates(pathPoint.Lat, pathPoint.Lng))
                    {
                        _logger.LogWarning("Skipping path point with invalid coordinates: Lat={Lat}, Lng={Lng}", 
                            pathPoint.Lat, pathPoint.Lng);
                        continue;
                    }

                    validPathPoints.Add(pathPoint);
                    createdIds.Add(pathPoint.Id.ToString());
                }

                if (!validPathPoints.Any())
                {
                    _logger.LogWarning("No valid path points to insert after validation");
                    return new List<string>();
                }

                // Group by vessel for duplicate checking
                var pathPointsByVessel = validPathPoints.GroupBy(p => p.VesselId).ToList();
                var finalPathPoints = new List<VesselPathPoint>();

                foreach (var vesselGroup in pathPointsByVessel)
                {
                    var vesselId = vesselGroup.Key;
                    var vesselPathPoints = vesselGroup.OrderBy(p => p.Timestamp).ToList();

                    // Get existing path points for this vessel
                    var existingPoints = await _databaseService.SelectAsync<VesselPathPoint>(new { VesselId = vesselId });
                    var existingTimestamps = existingPoints?.Where(p => p.Timestamp.HasValue)
                        .Select(p => p.Timestamp.Value).ToHashSet() ?? new HashSet<DateTime>();

                    // Filter out duplicates (within 1 minute)
                    foreach (var pathPoint in vesselPathPoints)
                    {
                        if (!pathPoint.Timestamp.HasValue)
                        {
                            finalPathPoints.Add(pathPoint);
                            continue;
                        }

                        var isDuplicate = existingTimestamps.Any(existing =>
                            Math.Abs((existing - pathPoint.Timestamp.Value).TotalMinutes) < 1);

                        if (!isDuplicate)
                        {
                            finalPathPoints.Add(pathPoint);
                            existingTimestamps.Add(pathPoint.Timestamp.Value); // Track within this batch too
                        }
                        else
                        {
                            _logger.LogDebug("Skipping duplicate path point for vessel {VesselId} at timestamp {Timestamp}", 
                                vesselId, pathPoint.Timestamp);
                        }
                    }
                }

                // Bulk insert all valid, non-duplicate path points
                if (finalPathPoints.Any())
                {
                    // Use individual inserts for now - can optimize to true bulk insert later if needed
                    var actualCreatedIds = new List<string>();
                    foreach (var pathPoint in finalPathPoints)
                    {
                        try
                        {
                            await _databaseService.InsertAsync(pathPoint);
                            actualCreatedIds.Add(pathPoint.Id.ToString());
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Failed to insert individual path point {Id} for vessel {VesselId}", 
                                pathPoint.Id, pathPoint.VesselId);
                        }
                    }

                    _logger.LogInformation("Successfully created {Count} vessel path points from {TotalCount} provided", 
                        actualCreatedIds.Count, pathPoints.Count);

                    return actualCreatedIds;
                }

                return new List<string>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating vessel path points in bulk");
                throw;
            }
        }
    }
}