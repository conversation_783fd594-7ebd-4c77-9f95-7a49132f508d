using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using Microsoft.Extensions.Logging;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Services
{
    public partial class VesselPathPointService : IVesselPathPointService
    {
        private readonly ILogger<VesselPathPointService> _logger;
        private readonly IDatabaseService _databaseService;

        public VesselPathPointService(
            ILogger<VesselPathPointService> logger,
            IDatabaseService databaseService)
        {
            _logger = logger;
            _databaseService = databaseService;
        }

        public (float? lat, float? lng)? ParseCoordinates(string coordinateString)
        {
            if (string.IsNullOrWhiteSpace(coordinateString))
                return null;

            try
            {
                var cleanCoordinates = coordinateString.Trim();

                // Handle common coordinate formats:
                // "40.7128,-74.0060" (decimal degrees)
                // "40.7128, -74.0060" (with spaces)
                // "40°42'46.0\"N 74°00'21.6\"W" (degrees/minutes/seconds - not implemented in this version)
                
                // Split by comma and extract lat/lng
                var parts = cleanCoordinates.Split(new char[] { ',', ';' }, StringSplitOptions.RemoveEmptyEntries);
                
                if (parts.Length >= 2)
                {
                    var latString = parts[0].Trim();
                    var lngString = parts[1].Trim();
                    
                    // Try to parse as decimal degrees
                    if (float.TryParse(latString, NumberStyles.Float, CultureInfo.InvariantCulture, out var lat) &&
                        float.TryParse(lngString, NumberStyles.Float, CultureInfo.InvariantCulture, out var lng))
                    {
                        return ValidateCoordinates(lat, lng) ? (lat, lng) : null;
                    }
                }

                // Try parsing as a single space-separated string (some formats might use spaces)
                parts = cleanCoordinates.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length >= 2)
                {
                    if (float.TryParse(parts[0], NumberStyles.Float, CultureInfo.InvariantCulture, out var lat) &&
                        float.TryParse(parts[1], NumberStyles.Float, CultureInfo.InvariantCulture, out var lng))
                    {
                        return ValidateCoordinates(lat, lng) ? (lat, lng) : null;
                    }
                }

                _logger.LogWarning("Unable to parse coordinate string: '{CoordinateString}'", coordinateString);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error parsing coordinate string: '{CoordinateString}'", coordinateString);
                return null;
            }
        }

        public bool ValidateCoordinates(float? lat, float? lng)
        {
            if (!lat.HasValue || !lng.HasValue)
                return false;

            // Validate latitude range: -90 to 90 degrees
            if (lat.Value < -90.0f || lat.Value > 90.0f)
                return false;

            // Validate longitude range: -180 to 180 degrees
            if (lng.Value < -180.0f || lng.Value > 180.0f)
                return false;

            // Check for obviously invalid coordinates (0,0 might be valid for Gulf of Guinea, but often indicates missing data)
            // For now, we'll allow (0,0) but log a warning
            if (lat.Value == 0.0f && lng.Value == 0.0f)
            {
                _logger.LogWarning("Coordinates (0,0) detected - this might indicate missing location data");
                return true; // Still valid, but suspicious
            }

            return true;
        }
    }
}