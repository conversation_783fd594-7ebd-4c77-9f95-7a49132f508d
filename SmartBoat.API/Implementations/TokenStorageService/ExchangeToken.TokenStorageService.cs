using SmartBoat.API.Types;
using System;
using System.Threading.Tasks;
using System.Net.Http;
using System.Text.Json;
using System.Collections.Generic;

namespace SmartBoat.API.Services
{
    public partial class TokenStorageService
    {
        public async Task<Response<SaveTokenDto>> ExchangeTokenAsync(ExchangeTokenDto exchangeDto, Guid userId)
        {
            try
            {
                _logger.LogInformation("Starting token exchange for user {UserId}", userId);

                if (exchangeDto == null || string.IsNullOrWhiteSpace(exchangeDto.AuthorizationCode) || string.IsNullOrWhiteSpace(exchangeDto.CodeVerifier))
                {
                    _logger.LogInformation($"TOKEN-422: Client Error. Missing required exchange parameters. UserId: {userId}");
                    return new Response<SaveTokenDto>
                    {
                        Payload = null,
                        Exception = new ResponseException
                        {
                            Id = Guid.NewGuid(),
                            Code = "TOKEN-422",
                            Description = "Authorization code and code verifier are required",
                            Category = "Client Error"
                        }
                    };
                }

                // Microsoft Graph configuration
                var clientId = "4d59239e-ff4d-4bd9-b825-e61d87a1a6e9";
                var tenantId = "72d7af18-45b8-4c9a-ac32-d056c6bda0f5";
                var tokenEndpoint = $"https://login.microsoftonline.com/{tenantId}/oauth2/v2.0/token";

                using var httpClient = new HttpClient();

                // Prepare the token exchange request
                var formData = new List<KeyValuePair<string, string>>
                {
                    new("grant_type", "authorization_code"),
                    new("client_id", clientId),
                    new("code", exchangeDto.AuthorizationCode),
                    new("redirect_uri", exchangeDto.RedirectUri),
                    new("code_verifier", exchangeDto.CodeVerifier),
                    // Request offline_access to ensure a refresh token is issued
                    new("scope", "User.Read Mail.Read offline_access")
                };

                var formContent = new FormUrlEncodedContent(formData);

                _logger.LogInformation("Sending token exchange request to Microsoft");

                // Make the token exchange request
                var response = await httpClient.PostAsync(tokenEndpoint, formContent);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError("Token exchange failed. Status: {StatusCode}, Response: {Response}", 
                        response.StatusCode, responseContent);
                    
                    return new Response<SaveTokenDto>
                    {
                        Payload = null,
                        Exception = new ResponseException
                        {
                            Id = Guid.NewGuid(),
                            Code = "TOKEN-EXCHANGE-FAILED",
                            Description = $"Token exchange failed: {responseContent}",
                            Category = "External API Error"
                        }
                    };
                }

                // Parse the token response
                var tokenResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);
                
                var accessToken = tokenResponse.GetProperty("access_token").GetString();
                var refreshToken = tokenResponse.TryGetProperty("refresh_token", out var refreshProp) 
                    ? refreshProp.GetString() 
                    : null;
                var expiresIn = tokenResponse.GetProperty("expires_in").GetInt32();
                var scope = tokenResponse.TryGetProperty("scope", out var scopeProp) 
                    ? scopeProp.GetString() 
                    : "User.Read Mail.Read";

                // Create the SaveTokenDto with the exchanged tokens
                var saveTokenDto = new SaveTokenDto
                {
                    AccessToken = accessToken ?? string.Empty,
                    RefreshToken = refreshToken,
                    ExpiresAt = DateTime.UtcNow.AddSeconds(expiresIn),
                    Scope = scope
                };

                // Save the token using the existing SaveTokenAsync method
                var saveResult = await SaveTokenAsync(saveTokenDto, userId);
                
                if (saveResult.Exception != null)
                {
                    _logger.LogError("Failed to save exchanged token for user {UserId}: {Error}", 
                        userId, saveResult.Exception.Description);
                    return new Response<SaveTokenDto>
                    {
                        Payload = null,
                        Exception = new ResponseException
                        {
                            Id = Guid.NewGuid(),
                            Code = "TOKEN-SAVE-FAILED",
                            Description = "Failed to save token after exchange",
                            Category = "Database Error"
                        }
                    };
                }

                _logger.LogInformation("Token exchange and save completed successfully for user {UserId}", userId);

                return new Response<SaveTokenDto>
                {
                    Payload = saveTokenDto,
                    Exception = null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during token exchange for user {UserId}", userId);
                return new Response<SaveTokenDto>
                {
                    Payload = null,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "TOKEN-EXCHANGE-ERROR",
                        Description = "An error occurred during token exchange",
                        Category = "Technical Error"
                    }
                };
            }
        }
    }
}
