namespace SmartBoat.API.Types
{
    public class GeofencePolygon
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<GeofencePoint> Points { get; set; } = new List<GeofencePoint>();
    }

    public class GeofencePoint
    {
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public double Radius { get; set; }
    }
}