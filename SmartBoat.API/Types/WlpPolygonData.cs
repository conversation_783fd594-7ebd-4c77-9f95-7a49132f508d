using System.Text.Json;

namespace SmartBoat.API.Types
{
    public class WlpPolygonData
    {
        public Guid Id { get; set; }
        public Guid TemplateId { get; set; }
        public int ZoneId { get; set; }
        public string ZoneName { get; set; } = string.Empty;
        public string? ZoneDescription { get; set; }
        public string PolygonData { get; set; } = string.Empty; // JSON serialized
        public int PointCount { get; set; }
        public float MinLat { get; set; }
        public float MaxLat { get; set; }
        public float MinLng { get; set; }
        public float MaxLng { get; set; }
        public decimal BoundingBoxMinLat { get; set; }
        public decimal BoundingBoxMaxLat { get; set; }
        public decimal BoundingBoxMinLng { get; set; }
        public decimal BoundingBoxMaxLng { get; set; }

        // Audit fields
        public DateTime Created { get; set; }
        public Guid CreatedBy { get; set; }

        // Helper method to deserialize polygon points
        public List<GeofencePoint> GetPolygonPoints()
        {
            if (string.IsNullOrEmpty(PolygonData))
                return new List<GeofencePoint>();

            try
            {
                return JsonSerializer.Deserialize<List<GeofencePoint>>(PolygonData) ?? new List<GeofencePoint>();
            }
            catch
            {
                return new List<GeofencePoint>();
            }
        }

        // Helper method to serialize polygon points
        public void SetPolygonPoints(List<GeofencePoint> points)
        {
            PolygonData = JsonSerializer.Serialize(points);
            PointCount = points.Count;

            if (points.Count > 0)
            {
                BoundingBoxMinLat = (decimal)points.Min(p => p.Latitude);
                BoundingBoxMaxLat = (decimal)points.Max(p => p.Latitude);
                BoundingBoxMinLng = (decimal)points.Min(p => p.Longitude);
                BoundingBoxMaxLng = (decimal)points.Max(p => p.Longitude);
            }
        }
    }

    public class WlpPolygonDataDto
    {
        public Guid Id { get; set; }
        public Guid TemplateId { get; set; }
        public int ZoneId { get; set; }
        public string ZoneName { get; set; } = string.Empty;
        public string? ZoneDescription { get; set; }
        public string PolygonData { get; set; } = string.Empty;
        public int PointCount { get; set; }
        public float MinLat { get; set; }
        public float MaxLat { get; set; }
        public float MinLng { get; set; }
        public float MaxLng { get; set; }
        public decimal BoundingBoxMinLat { get; set; }
        public decimal BoundingBoxMaxLat { get; set; }
        public decimal BoundingBoxMinLng { get; set; }
        public decimal BoundingBoxMaxLng { get; set; }
    }
}