using System.Text.Json.Serialization;

namespace SmartBoat.API.Types
{
    public class WlpGeofenceData
    {
        [JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;

        [JsonPropertyName("version")]
        public string Version { get; set; } = string.Empty;

        [JsonPropertyName("mu")]
        public int Mu { get; set; }

        [JsonPropertyName("zones")]
        public List<WlpZone> Zones { get; set; } = new List<WlpZone>();
    }

    public class WlpZone
    {
        [JsonPropertyName("id")]
        public int Id { get; set; }

        [JsonPropertyName("n")]
        public string Name { get; set; } = string.Empty;

        [JsonPropertyName("d")]
        public string Description { get; set; } = string.Empty;

        [JsonPropertyName("rid")]
        public long ResourceId { get; set; }

        [JsonPropertyName("t")]
        public int Type { get; set; }

        [JsonPropertyName("w")]
        public int Width { get; set; }

        [JsonPropertyName("f")]
        public int Flags { get; set; }

        [JsonPropertyName("c")]
        public long Color { get; set; }

        [JsonPropertyName("tc")]
        public long TextColor { get; set; }

        [JsonPropertyName("ts")]
        public int TextSize { get; set; }

        [JsonPropertyName("min")]
        public int MinZoom { get; set; }

        [JsonPropertyName("max")]
        public int MaxZoom { get; set; }

        [JsonPropertyName("i")]
        public long Icon { get; set; }

        [JsonPropertyName("libId")]
        public int LibId { get; set; }

        [JsonPropertyName("path")]
        public string Path { get; set; } = string.Empty;

        [JsonPropertyName("b")]
        public WlpBoundingBox BoundingBox { get; set; } = new WlpBoundingBox();

        [JsonPropertyName("p")]
        public List<WlpPoint> Points { get; set; } = new List<WlpPoint>();

        [JsonPropertyName("ct")]
        public long CreateTime { get; set; }

        [JsonPropertyName("mt")]
        public long ModifyTime { get; set; }
    }

    public class WlpBoundingBox
    {
        [JsonPropertyName("min_x")]
        public double MinX { get; set; }

        [JsonPropertyName("min_y")]
        public double MinY { get; set; }

        [JsonPropertyName("max_x")]
        public double MaxX { get; set; }

        [JsonPropertyName("max_y")]
        public double MaxY { get; set; }

        [JsonPropertyName("cen_x")]
        public double CenterX { get; set; }

        [JsonPropertyName("cen_y")]
        public double CenterY { get; set; }
    }

    public class WlpPoint
    {
        [JsonPropertyName("x")]
        public double X { get; set; }

        [JsonPropertyName("y")]
        public double Y { get; set; }

        [JsonPropertyName("r")]
        public double Radius { get; set; }
    }
}