using System;

namespace SmartBoat.API.Types
{
    public class GetVesselFenceEventsDto
    {
        public Guid VesselId { get; set; }
        public int? PageLimit { get; set; } = 25;
        public int? PageOffset { get; set; } = 0;
        public string? Status { get; set; } // "Active", "Completed", or null for all
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
    }
}