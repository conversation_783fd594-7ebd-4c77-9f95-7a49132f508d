using System;

namespace SmartBoat.API.Types
{
    public class VesselFenceEventDto
    {
        public Guid Id { get; set; }
        public Guid VesselId { get; set; }
        public DateTime ExitTimestamp { get; set; }
        public float ExitLat { get; set; }
        public float ExitLng { get; set; }
        public DateTime? EntryTimestamp { get; set; }
        public float? EntryLat { get; set; }
        public float? EntryLng { get; set; }
        public int? DurationMinutes { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime Created { get; set; }
        public DateTime? Changed { get; set; }
        
        // Optional vessel information
        public string? VesselName { get; set; }
    }
}