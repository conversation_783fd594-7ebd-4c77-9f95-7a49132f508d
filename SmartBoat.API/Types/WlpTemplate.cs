using System.ComponentModel.DataAnnotations.Schema;

namespace SmartBoat.API.Types
{
    [Table("WlpTemplates")]
    public class WlpTemplate
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string OriginalFilename { get; set; } = string.Empty;
        public Guid UploadedBy { get; set; }
        public DateTime UploadTimestamp { get; set; }
        public bool IsActive { get; set; } = true;
        public bool IsDeleted { get; set; } = false;
        public long FileSize { get; set; }
        public int ZoneCount { get; set; }
        public string FileContentHash { get; set; } = string.Empty;
        
        // Audit fields
        public DateTime Created { get; set; }
        public Guid CreatedBy { get; set; }
        public DateTime? Changed { get; set; }
        public Guid? ChangedBy { get; set; }
    }

    public class WlpTemplateDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string OriginalFilename { get; set; } = string.Empty;
        public DateTime UploadTimestamp { get; set; }
        public long FileSize { get; set; }
        public int ZoneCount { get; set; }
        public List<string> ZoneNames { get; set; } = new List<string>();
        public DateTime Created { get; set; }
        public string CreatedByName { get; set; } = string.Empty;
        public string UploaderName { get; set; } = string.Empty; // Resolved from UploadedBy
    }

    public class CreateWlpTemplateDto
    {
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string OriginalFilename { get; set; } = string.Empty;
        public byte[] FileContent { get; set; } = Array.Empty<byte>();
    }

    public class UpdateWlpTemplateDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
    }

    public class WlpTemplateListRequestDto
    {
        public string? SearchTerm { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? PageOffset { get; set; }
        public int? PageLimit { get; set; }
    }
}