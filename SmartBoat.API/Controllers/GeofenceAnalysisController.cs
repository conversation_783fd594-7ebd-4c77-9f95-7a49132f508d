using Microsoft.AspNetCore.Mvc;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Types;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;

namespace SmartBoat.API.Controllers
{
    [ApiController]
    [Route("api/geofence-analysis")]
    [Authorize]
    public class GeofenceAnalysisController : ControllerBase
    {
        private readonly IGeofenceAnalysisService _geofenceAnalysisService;
        private readonly ILogger<GeofenceAnalysisController> _logger;

        public GeofenceAnalysisController(
            IGeofenceAnalysisService geofenceAnalysisService,
            ILogger<GeofenceAnalysisController> logger)
        {
            _geofenceAnalysisService = geofenceAnalysisService;
            _logger = logger;
        }

        private Guid GetUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
        }

        /// <summary>
        /// Create a new geofence analysis job
        /// </summary>
        [HttpPost("jobs/create")]
        public async Task<IActionResult> CreateAnalysisJob([FromBody] Request<CreateGeofenceAnalysisJobDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                if (request?.Payload == null)
                {
                    return BadRequest("Request payload is required");
                }

                if (request.Payload.VesselId == Guid.Empty)
                {
                    return BadRequest("Vessel ID is required");
                }

                if (!request.Payload.TemplateIds?.Any() == true)
                {
                    return BadRequest("At least one template ID is required");
                }

                if (request.Payload.AnalysisDateFrom >= request.Payload.AnalysisDateTo)
                {
                    return BadRequest("Analysis date from must be before analysis date to");
                }

                var result = await _geofenceAnalysisService.CreateAnalysisJobAsync(request.Payload, request.Header.UserId.Value);

                _logger.LogInformation("Created geofence analysis job {JobId} for user {UserId}", result.Id, request.Header.UserId.Value);

                return Ok(new Response<GeofenceAnalysisJobDto> { Payload = result });
            });
        }

        /// <summary>
        /// Get analysis jobs for the current user
        /// </summary>
        [HttpPost("jobs/list")]
        public async Task<IActionResult> GetAnalysisJobs([FromBody] Request<GetGeofenceAnalysisJobsDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var requestPayload = request?.Payload ?? new GetGeofenceAnalysisJobsDto();
                var result = await _geofenceAnalysisService.GetAnalysisJobsAsync(requestPayload, request.Header.UserId.Value);

                return Ok(new Response<List<GeofenceAnalysisJobDto>> { Payload = result });
            });
        }

        /// <summary>
        /// Get a specific analysis job by ID
        /// </summary>
        [HttpGet("jobs/{jobId}")]
        public async Task<IActionResult> GetAnalysisJob(Guid jobId)
        {
            try
            {
                var userId = GetUserId();
                if (userId == Guid.Empty)
                {
                    return Unauthorized("Invalid authorization");
                }

                var result = await _geofenceAnalysisService.GetAnalysisJobByIdAsync(jobId, userId);
                if (result == null)
                {
                    return NotFound("Analysis job not found");
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting analysis job {JobId}", jobId);
                return StatusCode(500, "An error occurred while retrieving the analysis job");
            }
        }

        /// <summary>
        /// Cancel a running analysis job
        /// </summary>
        [HttpPost("jobs/{jobId}/cancel")]
        public async Task<IActionResult> CancelAnalysisJob(Guid jobId)
        {
            try
            {
                var userId = GetUserId();
                if (userId == Guid.Empty)
                {
                    return Unauthorized("Invalid authorization");
                }

                var result = await _geofenceAnalysisService.CancelAnalysisJobAsync(jobId, userId);
                if (!result)
                {
                    return NotFound("Analysis job not found or cannot be cancelled");
                }

                _logger.LogInformation("Cancelled analysis job {JobId} for user {UserId}", jobId, userId);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling analysis job {JobId}", jobId);
                return StatusCode(500, "An error occurred while cancelling the analysis job");
            }
        }

        /// <summary>
        /// Delete an analysis job
        /// </summary>
        [HttpDelete("jobs/{jobId}")]
        public async Task<IActionResult> DeleteAnalysisJob(Guid jobId)
        {
            try
            {
                var userId = GetUserId();
                if (userId == Guid.Empty)
                {
                    return Unauthorized("Invalid authorization");
                }

                var result = await _geofenceAnalysisService.DeleteAnalysisJobAsync(jobId, userId);
                if (!result)
                {
                    return NotFound("Analysis job not found");
                }

                _logger.LogInformation("Deleted analysis job {JobId} for user {UserId}", jobId, userId);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting analysis job {JobId}", jobId);
                return StatusCode(500, "An error occurred while deleting the analysis job");
            }
        }

        /// <summary>
        /// Get fence events for a specific analysis job
        /// </summary>
        [HttpGet("jobs/{jobId}/fence-events")]
        public async Task<IActionResult> GetJobFenceEvents(Guid jobId)
        {
            try
            {
                var userId = GetUserId();
                if (userId == Guid.Empty)
                {
                    return Unauthorized("Invalid authorization");
                }

                var result = await _geofenceAnalysisService.GetJobFenceEventsAsync(jobId, userId);
                return Ok(result);
            }
            catch (UnauthorizedAccessException)
            {
                return NotFound("Analysis job not found or access denied");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting fence events for job {JobId}", jobId);
                return StatusCode(500, "An error occurred while retrieving fence events");
            }
        }

        /// <summary>
        /// Get fence events with filtering and pagination
        /// </summary>
        [HttpPost("fence-events")]
        public async Task<IActionResult> GetFenceEvents([FromBody] Request<GetFenceEventsDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var requestPayload = request?.Payload ?? new GetFenceEventsDto();
                var result = await _geofenceAnalysisService.GetFenceEventsAsync(requestPayload, request.Header.UserId.Value);

                return Ok(new Response<List<GeofenceFenceEventDto>> { Payload = result });
            });
        }

        /// <summary>
        /// Get fence event summary statistics
        /// </summary>
        [HttpPost("fence-events/summary")]
        public async Task<IActionResult> GetFenceEventSummary([FromBody] Request<GetFenceEventSummaryDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                if (request?.Payload == null)
                {
                    return BadRequest("Request payload is required");
                }

                var result = await _geofenceAnalysisService.GetFenceEventSummaryAsync(
                    request.Payload.JobId,
                    request.Payload.VesselId,
                    request.Payload.FromDate,
                    request.Payload.ToDate,
                    request.Header.UserId.Value);

                return Ok(new Response<object> { Payload = result });
            });
        }
    }
}