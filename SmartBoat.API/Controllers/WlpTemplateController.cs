using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Types;
using System.ComponentModel.DataAnnotations;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;

namespace SmartBoat.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class WlpTemplateController : ControllerBase
    {
        private readonly IWlpTemplateService _wlpTemplateService;
        private readonly ILogger<WlpTemplateController> _logger;

        public WlpTemplateController(
            IWlpTemplateService wlpTemplateService,
            ILogger<WlpTemplateController> logger)
        {
            _wlpTemplateService = wlpTemplateService;
            _logger = logger;
        }

        [HttpPost("create")]
        public async Task<IActionResult> CreateTemplate([FromBody] Request<CreateWlpTemplatePayloadDto> request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var userId = request.Header.UserId.Value;

                // Convert base64 file content to bytes
                byte[] fileContent;
                try
                {
                    fileContent = Convert.FromBase64String(request.Payload.FileContentBase64);
                }
                catch (FormatException)
                {
                    return BadRequest("Invalid file content format. Expected base64 string.");
                }

                var createDto = new CreateWlpTemplateDto
                {
                    Name = request.Payload.Name,
                    Description = request.Payload.Description,
                    FileName = request.Payload.FileName,
                    OriginalFilename = request.Payload.OriginalFilename ?? request.Payload.FileName,
                    FileContent = fileContent
                };

                var result = await _wlpTemplateService.CreateTemplateAsync(createDto, userId);

                return Ok(new Response<WlpTemplateDto> { Payload = result });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation creating WLP template");
                return BadRequest(new Response<object> { Exception = new ResponseException { Description = ex.Message } });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating WLP template");
                return StatusCode(500, new Response<object> { Exception = new ResponseException { Description = "Internal server error" } });
            }
        }

        [HttpPost("list")]
        public async Task<IActionResult> GetTemplates([FromBody] Request<WlpTemplateListPayloadDto> request)
        {
            try
            {
                var userId = request.Header.UserId.Value;

                var listRequest = new WlpTemplateListRequestDto
                {
                    SearchTerm = request.Payload.SearchTerm,
                    FromDate = request.Payload.FromDate,
                    ToDate = request.Payload.ToDate,
                    PageOffset = request.Payload.PageOffset,
                    PageLimit = request.Payload.PageLimit
                };

                var result = await _wlpTemplateService.GetTemplatesAsync(listRequest, userId);

                return Ok(new Response<List<WlpTemplateDto>> { Payload = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting WLP templates");
                return StatusCode(500, new Response<object> { Exception = new ResponseException { Description = "Internal server error" } });
            }
        }

        [HttpPost("get")]
        public async Task<IActionResult> GetTemplate([FromBody] Request<GetWlpTemplatePayloadDto> request)
        {
            try
            {
                var userId = request.Header.UserId.Value;

                var result = await _wlpTemplateService.GetTemplateByIdAsync(request.Payload.TemplateId, userId);

                if (result == null)
                {
                    return NotFound(new Response<object> { Exception = new ResponseException { Description = "Template not found" } });
                }

                return Ok(new Response<WlpTemplateDto> { Payload = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting WLP template");
                return StatusCode(500, new Response<object> { Exception = new ResponseException { Description = "Internal server error" } });
            }
        }

        [HttpPost("update")]
        public async Task<IActionResult> UpdateTemplate([FromBody] Request<UpdateWlpTemplatePayloadDto> request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var userId = request.Header.UserId.Value;

                var updateDto = new UpdateWlpTemplateDto
                {
                    Id = request.Payload.Id,
                    Name = request.Payload.Name,
                    Description = request.Payload.Description
                };

                var result = await _wlpTemplateService.UpdateTemplateAsync(updateDto, userId);

                return Ok(new Response<WlpTemplateDto> { Payload = result });
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid argument updating WLP template");
                return NotFound(new Response<object> { Exception = new ResponseException { Description = ex.Message } });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating WLP template");
                return StatusCode(500, new Response<object> { Exception = new ResponseException { Description = "Internal server error" } });
            }
        }

        [HttpPost("delete")]
        public async Task<IActionResult> DeleteTemplate([FromBody] Request<DeleteWlpTemplatePayloadDto> request)
        {
            try
            {
                var userId = request.Header.UserId.Value;

                var success = await _wlpTemplateService.DeleteTemplateAsync(request.Payload.TemplateId, userId);

                if (!success)
                {
                    return NotFound(new Response<object> { Exception = new ResponseException { Description = "Template not found" } });
                }

                return Ok(new Response<string> { Payload = "Template deleted successfully" });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation deleting WLP template");
                return BadRequest(new Response<object> { Exception = new ResponseException { Description = ex.Message } });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting WLP template");
                return StatusCode(500, new Response<object> { Exception = new ResponseException { Description = "Internal server error" } });
            }
        }

        [HttpPost("polygons")]
        public async Task<IActionResult> GetTemplatePolygons([FromBody] Request<********************************> request)
        {
            try
            {
                var userId = request.Header.UserId.Value;

                var result = await _wlpTemplateService.GetTemplatePolygonsAsync(request.Payload.TemplateId, userId);

                return Ok(new Response<List<WlpPolygonDataDto>> { Payload = result });
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid argument getting template polygons");
                return NotFound(new Response<object> { Exception = new ResponseException { Description = ex.Message } });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting template polygons");
                return StatusCode(500, new Response<object> { Exception = new ResponseException { Description = "Internal server error" } });
            }
        }

        [HttpPost("check-duplicate")]
        public async Task<IActionResult> CheckDuplicate([FromBody] Request<CheckDuplicateWlpTemplatePayloadDto> request)
        {
            try
            {
                var userId = request.Header.UserId.Value;

                // Convert base64 to bytes and calculate hash
                byte[] fileContent;
                try
                {
                    fileContent = Convert.FromBase64String(request.Payload.FileContentBase64);
                }
                catch (FormatException)
                {
                    return BadRequest(new Response<object> { Exception = new ResponseException { Description = "Invalid file content format. Expected base64 string." } });
                }

                var hash = System.Security.Cryptography.SHA256.HashData(fileContent);
                var hashString = Convert.ToHexString(hash);

                var duplicate = await _wlpTemplateService.FindDuplicateTemplateAsync(hashString, userId);

                var result = new {
                    isDuplicate = duplicate != null,
                    existingTemplate = duplicate
                };

                return Ok(new Response<object> { Payload = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking for duplicate template");
                return StatusCode(500, new Response<object> { Exception = new ResponseException { Description = "Internal server error" } });
            }
        }


    }

    // Payload DTOs for the standard Request<T> envelope format
    public class CreateWlpTemplatePayloadDto
    {
        [Required]
        [StringLength(200, MinimumLength = 1)]
        public required string Name { get; set; }

        public string? Description { get; set; }

        [Required]
        [StringLength(255, MinimumLength = 1)]
        public required string FileName { get; set; }

        [StringLength(255)]
        public string? OriginalFilename { get; set; }

        [Required]
        public required string FileContentBase64 { get; set; }
    }

    public class WlpTemplateListPayloadDto
    {
        public string? SearchTerm { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? PageOffset { get; set; }
        public int? PageLimit { get; set; }
    }

    public class GetWlpTemplatePayloadDto
    {
        [Required]
        public Guid TemplateId { get; set; }
    }

    public class UpdateWlpTemplatePayloadDto
    {
        [Required]
        public Guid Id { get; set; }

        [Required]
        [StringLength(200, MinimumLength = 1)]
        public required string Name { get; set; }

        public string? Description { get; set; }
    }

    public class DeleteWlpTemplatePayloadDto
    {
        [Required]
        public Guid TemplateId { get; set; }
    }

    public class ********************************
    {
        [Required]
        public Guid TemplateId { get; set; }
    }

    public class CheckDuplicateWlpTemplatePayloadDto
    {
        [Required]
        public required string FileContentBase64 { get; set; }
    }
}