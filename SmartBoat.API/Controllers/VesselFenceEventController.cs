using Microsoft.AspNetCore.Mvc;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Attributes;
using System.Threading.Tasks;

namespace SmartBoat.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [JwtAuthorize]
    public class VesselFenceEventController : ControllerBase
    {
        private readonly IGeoFencingService _geoFencingService;

        public VesselFenceEventController(IGeoFencingService geoFencingService)
        {
            _geoFencingService = geoFencingService;
        }

        [HttpPost("vessel-events")]
        [ProducesResponseType(200, Type = typeof(Response<List<VesselFenceEventDto>>))]
        public async Task<IActionResult> GetVesselFenceEvents([FromBody] Request<GetVesselFenceEventsDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var events = await _geoFencingService.GetVesselFenceEventsAsync(request.Payload);
                return Ok(new Response<List<VesselFenceEventDto>> { Payload = events });
            });
        }

        [HttpPost("active-events")]
        [ProducesResponseType(200, Type = typeof(Response<List<VesselFenceEventDto>>))]
        public async Task<IActionResult> GetActiveFenceEvents([FromBody] Request<object> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var activeEvents = await _geoFencingService.GetActiveFenceEventsAsync();
                return Ok(new Response<List<VesselFenceEventDto>> { Payload = activeEvents });
            });
        }

        [HttpPost("check-coordinates")]
        [ProducesResponseType(200, Type = typeof(Response<CheckCoordinatesResultDto>))]
        public async Task<IActionResult> CheckCoordinates([FromBody] Request<CheckCoordinatesDto> request)
        {
            return await SafeExecutor.ExecuteAsync(async () =>
            {
                var isInside = _geoFencingService.IsInsideGeoFence(request.Payload.Lat, request.Payload.Lng);
                
                return Ok(new Response<CheckCoordinatesResultDto>
                {
                    Payload = new CheckCoordinatesResultDto
                    {
                        Lat = request.Payload.Lat,
                        Lng = request.Payload.Lng,
                        IsInsideGeoFence = isInside
                    }
                });
            });
        }
    }
}