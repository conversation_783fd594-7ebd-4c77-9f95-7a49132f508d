#!/usr/bin/env node

/**
 * Test script to verify WLP Template list endpoint
 * Tests that the created template is properly stored and retrievable
 */

const https = require('https');
const crypto = require('crypto');

// Test configuration
const API_BASE_URL = 'https://localhost:7001';
const APPLICATION_ID = '03FC0B90-DFAD-11EE-8D86-0800200C9A66';

// JWT configuration from appsettings.Development.json
const JWT_CONFIG = {
  secretKey: 'SmartBoatSecretKeyForJWTTokenGeneration2024!@#$%^&*()_+',
  issuer: 'SmartBoat.API',
  audience: 'SmartBoat.API',
  expirationMinutes: 60
};

// Function to create a valid JWT token
function createValidJwtToken(userId = '********-1234-1234-1234-************') {
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  };

  const payload = {
    'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier': userId,
    'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress': '<EMAIL>',
    'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name': 'testuser',
    'http://schemas.microsoft.com/ws/2008/06/identity/claims/role': 'Administrator',
    'FirstName': 'Test',
    'LastName': 'User',
    iss: JWT_CONFIG.issuer,
    aud: JWT_CONFIG.audience,
    exp: Math.floor(Date.now() / 1000) + (JWT_CONFIG.expirationMinutes * 60),
    iat: Math.floor(Date.now() / 1000)
  };

  const encodedHeader = Buffer.from(JSON.stringify(header)).toString('base64url');
  const encodedPayload = Buffer.from(JSON.stringify(payload)).toString('base64url');
  
  const signature = crypto
    .createHmac('sha256', JWT_CONFIG.secretKey)
    .update(`${encodedHeader}.${encodedPayload}`)
    .digest('base64url');

  return `${encodedHeader}.${encodedPayload}.${signature}`;
}

// Create request envelope in the correct format
const createRequestEnvelope = (payload) => {
  return {
    Header: {
      Id: crypto.randomUUID(),
      Application: APPLICATION_ID,
      Bank: "BANK",
      UserId: "********-1234-1234-1234-************" // Same user ID as creation
    },
    Payload: payload
  };
};

// Test payload for list request
const testPayload = {
  searchTerm: null,
  fromDate: null,
  toDate: null,
  pageOffset: null,
  pageLimit: null
};

const requestBody = JSON.stringify(createRequestEnvelope(testPayload));

console.log('Testing WLP Template List Endpoint...');
console.log('Request URL:', `${API_BASE_URL}/api/WlpTemplate/list`);

const options = {
  hostname: 'localhost',
  port: 7001,
  path: '/api/WlpTemplate/list',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(requestBody),
    'Authorization': `Bearer ${createValidJwtToken()}` // Valid JWT token
  },
  rejectUnauthorized: false // Accept self-signed certificates
};

const req = https.request(options, (res) => {
  console.log('\n--- Response ---');
  console.log('Status Code:', res.statusCode);

  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    console.log('\nResponse Body:');
    try {
      const jsonResponse = JSON.parse(data);
      console.log(JSON.stringify(jsonResponse, null, 2));

      if (res.statusCode === 200) {
        console.log('\n✅ SUCCESS: List request completed successfully!');
        if (jsonResponse.payload && jsonResponse.payload.length > 0) {
          console.log(`📋 Found ${jsonResponse.payload.length} WLP template(s) in database`);
          jsonResponse.payload.forEach((template, index) => {
            console.log(`   ${index + 1}. ${template.name} (${template.zoneCount} zones, ${template.fileSize} bytes)`);
          });
        } else {
          console.log('📋 No WLP templates found in database');
        }
      } else {
        console.log(`\n⚠️  UNEXPECTED STATUS: ${res.statusCode}`);
      }
    } catch (error) {
      console.log('Raw response:', data);
      console.log('Parse error:', error.message);
    }
  });
});

req.on('error', (error) => {
  console.error('\n❌ REQUEST ERROR:', error.message);
  if (error.code === 'ECONNREFUSED') {
    console.log('Make sure the SmartBoat API is running on https://localhost:7001');
  }
});

req.write(requestBody);
req.end();
