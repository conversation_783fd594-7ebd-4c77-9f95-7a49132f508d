# WLP Geofence Implementation Documentation

## Overview

This document describes the complete implementation of the WLP (Wialon) geofence system for the SmartBoat platform. The system replaces the previous Athens-centered circular geofencing with proper polygon-based territorial boundary detection using actual Greek maritime boundary data.

## Architecture

### Core Components

1. **WLP File Parser** (`WlpGeofenceParser`)
2. **Polygon Geometry Service** (`PolygonGeofenceService`) 
3. **Main Geofencing Service** (`GeoFencingService`)
4. **Data Models** (WLP and Geofence types)

### File Structure

```
SmartBoat.API/
├── Implementations/GeoFencingService/
│   ├── GeoFencingService.cs          # Main service implementation
│   └── geofenceData.wlp              # 544KB WLP boundary data file
├── Services/
│   ├── WlpGeofenceParser.cs          # WLP file parsing
│   └── PolygonGeofenceService.cs     # Point-in-polygon algorithms
├── Interfaces/
│   ├── IGeoFencingService.cs         # Main service interface
│   ├── IWlpGeofenceParser.cs         # Parser interface
│   └── IPolygonGeofenceService.cs    # Geometry interface
└── Types/
    ├── WlpGeofenceData.cs            # WLP file data models
    └── GeofencePolygon.cs            # Internal polygon models
```

## Data Models

### WLP File Format

The `geofenceData.wlp` file contains Greek territorial boundary data in JSON format:

```json
{
  "type": "avl_resource",
  "version": "b4",
  "zones": [
    {
      "id": 1,
      "name": "Alimos Territory", 
      "description": "Athens area",
      "p": [
        {"x": 23.123, "y": 37.456, "r": 10},
        {"x": 23.789, "y": 37.654, "r": 10}
      ]
    }
  ]
}
```

### Internal Data Models

```csharp
public class WlpGeofenceData
{
    [JsonPropertyName("zones")]
    public List<WlpZone> Zones { get; set; } = new List<WlpZone>();
}

public class GeofencePolygon  
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<GeofencePoint> Points { get; set; } = new List<GeofencePoint>();
}

public class GeofencePoint
{
    public double Longitude { get; set; }  // x coordinate
    public double Latitude { get; set; }   // y coordinate  
    public double Radius { get; set; }
}
```

## Core Services

### 1. WLP Parser Service (`WlpGeofenceParser`)

**Purpose**: Parse WLP files and convert to internal polygon format

**Key Methods**:
```csharp
Task<WlpGeofenceData?> ParseWlpFileAsync(string filePath)
List<GeofencePolygon> ConvertToGeofencePolygons(WlpGeofenceData wlpData)
```

**Implementation**:
- Uses `System.Text.Json` for parsing
- Converts WLP coordinates (x=lng, y=lat) to internal format
- Handles file not found and JSON parsing errors
- Logs zone count and polygon conversion details

### 2. Polygon Geometry Service (`PolygonGeofenceService`)

**Purpose**: Point-in-polygon calculations using ray casting algorithm

**Key Methods**:
```csharp
bool IsPointInPolygon(double latitude, double longitude, List<GeofencePoint> polygonPoints)
GeofencePolygon? FindContainingPolygon(double latitude, double longitude, List<GeofencePolygon> polygons)
double CalculateMinDistanceToPolygon(double latitude, double longitude, List<GeofencePoint> polygonPoints)
```

**Ray Casting Algorithm**:
```csharp
private bool IsPointInPolygonRayCasting(double latitude, double longitude, List<GeofencePoint> polygonPoints)
{
    bool isInside = false;
    int pointCount = polygonPoints.Count;

    for (int i = 0, j = pointCount - 1; i < pointCount; j = i++)
    {
        var pi = polygonPoints[i];
        var pj = polygonPoints[j];

        if (((pi.Latitude > latitude) != (pj.Latitude > latitude)) &&
            (longitude < (pj.Longitude - pi.Longitude) * (latitude - pi.Latitude) / (pj.Latitude - pi.Latitude) + pi.Longitude))
        {
            isInside = !isInside;
        }
    }
    return isInside;
}
```

### 3. Main Geofencing Service (`GeoFencingService`)

**Purpose**: Primary geofencing logic, vessel tracking, and fence event management

**Key Methods**:
```csharp
Task CheckVesselPositionAsync(Guid vesselId, float lat, float lng, DateTime timestamp)
Task<List<VesselFenceEventDto>> GetVesselFenceEventsAsync(GetVesselFenceEventsDto request)
Task<List<VesselFenceEventDto>> GetActiveFenceEventsAsync()
bool IsInsideGeoFence(float lat, float lng)
string GetGeofenceZoneName(float lat, float lng)
double CalculateDistanceKm(float lat1, float lng1, float lat2, float lng2)
```

**Initialization Process**:
```csharp
private async Task InitializeGeofencesAsync()
{
    var wlpFilePath = Path.Combine(_environment.ContentRootPath, 
        "Implementations", "GeoFencingService", "geofenceData.wlp");
    
    var wlpData = await _wlpParser.ParseWlpFileAsync(wlpFilePath);
    if (wlpData != null)
    {
        _geofencePolygons = _wlpParser.ConvertToGeofencePolygons(wlpData);
        _logger.LogInformation("Successfully loaded {PolygonCount} geofence polygons", _geofencePolygons.Count);
    }
}
```

## Vessel Tracking Logic

### Fence Event Detection

The system tracks vessel movements and creates fence events when vessels cross boundaries:

```csharp
public async Task CheckVesselPositionAsync(Guid vesselId, float lat, float lng, DateTime timestamp)
{
    bool isInsideFence = IsInsideGeoFence(lat, lng);
    
    // Get the most recent active fence event for this vessel
    var activeEvents = await _databaseService.SelectAsync<VesselFenceEvent>(new { VesselId = vesselId, Status = "Active" });
    var activeEvent = activeEvents?.OrderByDescending(e => e.ExitTimestamp).FirstOrDefault();

    if (!isInsideFence && activeEvent == null)
    {
        // Vessel just exited the fence - create new event
        var newEvent = new VesselFenceEvent
        {
            Id = Guid.NewGuid(),
            VesselId = vesselId,
            ExitTimestamp = timestamp,
            ExitLat = lat,
            ExitLng = lng,
            Status = "Active",
            Created = DateTime.UtcNow
        };
        await _databaseService.InsertAsync(newEvent);
    }
    else if (isInsideFence && activeEvent != null)
    {
        // Vessel returned to fence - complete the event
        activeEvent.EntryTimestamp = timestamp;
        activeEvent.EntryLat = lat;
        activeEvent.EntryLng = lng;
        activeEvent.Status = "Completed";
        activeEvent.DurationMinutes = (int)Math.Round((timestamp - activeEvent.ExitTimestamp).TotalMinutes);
        await _databaseService.UpdateAsync<VesselFenceEvent>(activeEvent, new { Id = activeEvent.Id });
    }
}
```

### Event States

- **Active**: Vessel is currently outside the geofence
- **Completed**: Vessel has returned to the geofence, event is closed with duration calculated

## Dependency Injection Setup

```csharp
// Program.cs
builder.Services.AddScoped<IWlpGeofenceParser, WlpGeofenceParser>();
builder.Services.AddScoped<IPolygonGeofenceService, PolygonGeofenceService>();
builder.Services.AddScoped<IGeoFencingService, SmartBoat.API.Implementations.GeoFencingService.GeoFencingService>();
```

## API Endpoints

The geofencing system is accessible through existing controllers:

- `VesselFenceEventController` - Get fence events and active events
- Integration with vessel tracking systems for real-time position updates

## Testing Implementation

### Unit Test Structure

The test system uses **real WLP data** instead of mocks for accurate testing:

```csharp
public GeoFencingServiceTests()
{
    // Use REAL services instead of mocks
    var realWlpParser = new WlpGeofenceParser(Mock.Of<ILogger<WlpGeofenceParser>>());
    var realPolygonService = new PolygonGeofenceService(Mock.Of<ILogger<PolygonGeofenceService>>());
    
    // Setup real path to WLP file
    _mockEnvironment.Setup(e => e.ContentRootPath).Returns(
        Path.Combine(Directory.GetCurrentDirectory(), "..", "smartboat-platform", "SmartBoat.API"));
    
    _geoFencingService = new GeoFencingService(_mockDatabaseService.Object, _mockLogger.Object,
        realWlpParser, realPolygonService, _mockEnvironment.Object);
    
    // Allow time for async WLP file loading
    Task.Delay(1000).Wait();
}
```

### Test Data Files

- `testCoordsShouldNotHaveGoneOutsideFence.json`: Coordinates around Zakynthos/Kefalonia (~38.5 lat, ~20.9 lng)
- `testCoordsShouldHaveGoneOutsideFence.json`: Coordinates around Santorini (~36.8 lat, ~25.2 lng)

## Current Test Results with Real WLP Data

When using the actual WLP file, the test results show:

1. **Zakynthos/Kefalonia area coordinates** (~38.5 lat, ~20.9 lng): **OUTSIDE** geofence boundaries
2. **Santorini area coordinates** (~36.8 lat, ~25.2 lng): **OUTSIDE** geofence boundaries (returns "Unknown")

This indicates the WLP file may contain specific monitored zones rather than complete Greek territorial waters (15nm boundary).

## Key Insights

### What the WLP Data Actually Contains

Based on test results, the `geofenceData.wlp` file appears to contain:
- **Specific territorial zones** (like "Alimos Territory", "Corfu Territory") 
- **NOT the complete 15nm Greek territorial boundary**
- Zones are likely **high-traffic or monitored areas** rather than legal boundaries

### Zone Detection Behavior

- **Inside defined zones**: Returns specific zone name (e.g., "Alimos Territory")
- **Outside all zones**: Returns "Unknown" 
- **Boundary detection**: Uses precise polygon containment (ray casting algorithm)

## Performance Considerations

- **File Loading**: WLP file (544KB) loads once at service initialization
- **Point-in-Polygon**: O(n) complexity per polygon, where n = number of vertices
- **Caching**: Parsed polygons are cached in memory for fast lookup
- **Database Operations**: Fence events are stored in SQL Server with indexed queries

## Error Handling

- **File Not Found**: Logs error, continues with empty polygon list
- **JSON Parse Errors**: Catches JsonException, logs details, returns null
- **Invalid Polygons**: Skips zones with < 3 points, logs warning
- **Database Failures**: Logs errors, continues processing other vessels

## Future Enhancements

1. **Complete Territorial Waters**: Integration with official Greek maritime boundary data
2. **Performance Optimization**: Spatial indexing for large numbers of polygons
3. **Real-time Updates**: Support for dynamic geofence boundary updates
4. **Multi-Country Support**: Extension to handle multiple national boundaries
5. **Advanced Analytics**: Vessel route analysis and boundary violation patterns

## Migration Notes

### From Previous Implementation

The old Athens-centered circular geofencing has been completely replaced:

- **Old**: `distance <= 50km from Athens` 
- **New**: `point-in-polygon containment using WLP boundaries`

### Database Compatibility

The `VesselFenceEvent` table schema remains unchanged, ensuring backward compatibility for existing data.

### API Compatibility

All existing API endpoints continue to work with the new polygon-based system, maintaining client compatibility.