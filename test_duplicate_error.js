#!/usr/bin/env node

/**
 * Test script to verify improved error handling for duplicate WLP templates
 */

const https = require('https');
const fs = require('fs');

// Test configuration
const API_BASE_URL = 'https://localhost:7001';
const APPLICATION_ID = '03FC0B90-DFAD-11EE-8D86-0800200C9A66';

// Real JWT token provided by user
const REAL_JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLjbSgUD8t4OsO986AbYvl_OVJhsEaSkMOGUYzB3gic';

// Function to generate UUID v4
function uuidv4() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// Function to create request envelope
function createRequestEnvelope(payload, userId = '08ddcb9c-29ab-49d8-a7b9-faccf5caf282') {
  return {
    Header: {
      Id: uuidv4(),
      Application: APPLICATION_ID,
      Bank: "BANK",
      UserId: userId
    },
    Payload: payload
  };
}

// Function to make HTTPS request
function makeRequest(endpoint, data, token) {
  return new Promise((resolve, reject) => {
    const url = new URL(`${API_BASE_URL}${endpoint}`);
    
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json'
      },
      // Ignore SSL certificate errors for localhost testing
      rejectUnauthorized: false
    };

    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsedData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(JSON.stringify(data));
    req.end();
  });
}

// Main test function
async function testDuplicateError() {
  console.log('Testing WLP Template Duplicate Error Handling...');
  
  try {
    // Read the real .wlp file (same as existing template)
    const wlpFilePath = './SmartBoat.API/Implementations/GeoFencingService/geofenceData.wlp';
    
    if (!fs.existsSync(wlpFilePath)) {
      console.error('❌ WLP file not found:', wlpFilePath);
      return;
    }
    
    const wlpFileContent = fs.readFileSync(wlpFilePath, 'utf8');
    const base64Content = Buffer.from(wlpFileContent).toString('base64');
    
    console.log('✅ Successfully loaded WLP file');
    
    // Create the payload with the same content as existing template (should trigger duplicate error)
    const payload = {
      name: "Duplicate Test Template",
      description: "This should trigger a duplicate content error",
      fileName: "geofenceData.wlp",
      originalFilename: "geofenceData.wlp",
      fileContentBase64: base64Content
    };
    
    // Create the request envelope
    const requestData = createRequestEnvelope(payload);
    
    console.log('Request URL:', `${API_BASE_URL}/api/WlpTemplate/create`);
    console.log('Expected: 400 error with specific duplicate message');
    
    // Make the request
    const response = await makeRequest('/api/WlpTemplate/create', requestData, REAL_JWT_TOKEN);
    
    console.log('\n--- Response ---');
    console.log('Status Code:', response.statusCode);
    console.log('\nResponse Body:');
    console.log(JSON.stringify(response.data, null, 2));
    
    if (response.statusCode === 400) {
      const errorDescription = response.data?.exception?.description;
      if (errorDescription) {
        console.log('\n✅ SUCCESS: Backend returned specific error message:');
        console.log(`   "${errorDescription}"`);
        
        if (errorDescription.includes('already exists')) {
          console.log('✅ Error message correctly identifies duplicate content');
        } else {
          console.log('⚠️  Error message format may need improvement');
        }
      } else {
        console.log('\n❌ ERROR: Backend did not return structured error message');
      }
    } else {
      console.log('\n❌ ERROR: Expected 400 status code for duplicate content');
    }
    
  } catch (error) {
    console.error('❌ ERROR:', error.message);
  }
}

// Run the test
testDuplicateError();
