#!/usr/bin/env node

/**
 * Test script to list WLP templates with real JWT token
 */

const https = require('https');
const crypto = require('crypto');

// Test configuration
const API_BASE_URL = 'https://localhost:7001';
const APPLICATION_ID = '03FC0B90-DFAD-11EE-8D86-0800200C9A66';

// Real JWT token provided by user
const REAL_JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLjbSgUD8t4OsO986AbYvl_OVJhsEaSkMOGUYzB3gic';

// Function to generate UUID v4
function uuidv4() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// Function to create request envelope
function createRequestEnvelope(payload, userId = '08ddcb9c-29ab-49d8-a7b9-faccf5caf282') {
  return {
    Header: {
      Id: uuidv4(),
      Application: APPLICATION_ID,
      Bank: "BANK",
      UserId: userId
    },
    Payload: payload
  };
}

// Function to make HTTPS request
function makeRequest(endpoint, data, token) {
  return new Promise((resolve, reject) => {
    const url = new URL(`${API_BASE_URL}${endpoint}`);
    
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json'
      },
      // Ignore SSL certificate errors for localhost testing
      rejectUnauthorized: false
    };

    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsedData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(JSON.stringify(data));
    req.end();
  });
}

// Main test function
async function testWlpList() {
  console.log('Testing WLP Template List Endpoint...');
  
  try {
    // Create the payload for listing templates
    const payload = {
      searchTerm: "", // Empty to get all templates
      pageNumber: 1,
      pageSize: 50
    };
    
    // Create the request envelope
    const requestData = createRequestEnvelope(payload);
    
    console.log('Request URL:', `${API_BASE_URL}/api/WlpTemplate/list`);
    
    // Make the request
    const response = await makeRequest('/api/WlpTemplate/list', requestData, REAL_JWT_TOKEN);
    
    console.log('\n--- Response ---');
    console.log('Status Code:', response.statusCode);
    
    if (response.statusCode === 200) {
      console.log('\n✅ SUCCESS: WLP template list retrieved successfully!');
      
      const templates = response.data.payload || [];
      console.log(`\n📋 Found ${templates.length} template(s):`);
      
      templates.forEach((template, index) => {
        console.log(`\n${index + 1}. ${template.name}`);
        console.log(`   ID: ${template.id}`);
        console.log(`   Description: ${template.description || 'No description'}`);
        console.log(`   File: ${template.fileName} (${template.fileSize} bytes)`);
        console.log(`   Zones: ${template.zoneCount}`);
        console.log(`   Uploaded: ${new Date(template.uploadTimestamp).toLocaleString()}`);
        console.log(`   Hash: ${template.fileContentHash?.substring(0, 16)}...`);
      });
    } else {
      console.log('\n❌ ERROR: Failed to retrieve template list');
      console.log('Response Body:');
      console.log(JSON.stringify(response.data, null, 2));
    }
    
  } catch (error) {
    console.error('❌ ERROR:', error.message);
  }
}

// Run the test
testWlpList();
